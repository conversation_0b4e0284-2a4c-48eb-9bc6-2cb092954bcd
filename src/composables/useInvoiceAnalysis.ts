import { ref, computed } from 'vue'
import { analyzeInvoice, createRetryWrapper } from '@/utils/gemini'
import { useApiKeyStore } from '@/stores/apiKey'
import { useInvoicesStore } from '@/stores/invoices'
import { useProgressTracker, INVOICE_ANALYSIS_STAGES } from '@/composables/useProgressTracker'
import type { AnalysisResult } from '@/types'

export function useInvoiceAnalysis() {
  const apiKeyStore = useApiKeyStore()
  const invoicesStore = useInvoicesStore()
  const progressTracker = useProgressTracker()
  
  const isAnalyzing = ref(false)
  const analysisErrors = ref<Record<string, string>>({})
  const currentAnalysisId = ref<string | null>(null)

  const hasActiveAnalysis = computed(() => isAnalyzing.value)

  async function analyzeFile(file: File, invoiceId?: string): Promise<AnalysisResult> {
    if (!apiKeyStore.apiKey) {
      throw new Error('Brak klucza API. Skonfiguruj klucz w ustawieniach.')
    }

    const id = invoiceId || Date.now().toString()
    currentAnalysisId.value = id
    isAnalyzing.value = true
    delete analysisErrors.value[id]

    // Utwórz progress tracker
    progressTracker.createProgressTracker(id, INVOICE_ANALYSIS_STAGES)
    progressTracker.startProgress(id)

    try {
      // Stage 0: Upload (symulacja - plik już jest w pamięci)
      await simulateStage(id, 0, 500)
      progressTracker.completeStage(id, 0)

      // Stage 1: Konwersja do base64
      progressTracker.updateStageProgress(id, 1, 0)
      
      // Stage 2: Wysyłka do API
      progressTracker.completeStage(id, 1)
      progressTracker.updateStageProgress(id, 2, 0)

      const result = await createRetryWrapper(
        () => analyzeInvoiceWithProgress(file, apiKeyStore.apiKey!, id),
        3,
        2000
      )

      // Stage 5: Zapisywanie wyników
      progressTracker.updateStageProgress(id, 5, 50)
      
      if (invoiceId) {
        invoicesStore.updateInvoiceAnalysis(invoiceId, result)
      }

      progressTracker.completeProgress(id)
      
      return result

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Nieznany błąd'
      analysisErrors.value[id] = errorMessage
      progressTracker.setError(id, errorMessage)
      throw error

    } finally {
      isAnalyzing.value = false
      currentAnalysisId.value = null
      
      // Wyczyść progress po 5 sekund
      setTimeout(() => {
        progressTracker.removeProgress(id)
        delete analysisErrors.value[id]
      }, 5000)
    }
  }

  async function analyzeInvoiceWithProgress(
    file: File, 
    apiKey: string, 
    trackerId: string
  ): Promise<AnalysisResult> {
    // Stage 2: Wysyłka do API (zakończenie)
    progressTracker.completeStage(trackerId, 2)
    
    // Stage 3: Analiza AI
    progressTracker.updateStageProgress(trackerId, 3, 0)
    
    const result = await analyzeInvoice(
      file, 
      apiKey,
      (apiProgress) => {
        // Mapuj progress z API (0-100) na stage 3 i 4
        if (apiProgress <= 70) {
          // Stage 3: Analiza AI (0-70% API progress)
          const stageProgress = (apiProgress / 70) * 100
          progressTracker.updateStageProgress(trackerId, 3, stageProgress)
        } else {
          // Zakończ stage 3, rozpocznij stage 4
          if (apiProgress === 71) {
            progressTracker.completeStage(trackerId, 3)
          }
          
          // Stage 4: Przetwarzanie wyników (70-100% API progress)
          const stage4Progress = ((apiProgress - 70) / 30) * 100
          progressTracker.updateStageProgress(trackerId, 4, stage4Progress)
        }
      }
    )

    // Zakończ stage 4
    progressTracker.completeStage(trackerId, 4)
    
    return result
  }

  async function simulateStage(trackerId: string, stageIndex: number, duration: number) {
    const steps = 10
    const stepDuration = duration / steps
    
    for (let i = 0; i <= steps; i++) {
      const progress = (i / steps) * 100
      progressTracker.updateStageProgress(trackerId, stageIndex, progress)
      
      if (i < steps) {
        await new Promise(resolve => setTimeout(resolve, stepDuration))
      }
    }
  }

  async function analyzeBatch(files: { file: File; invoiceId: string }[]): Promise<AnalysisResult[]> {
    const results: AnalysisResult[] = []
    isAnalyzing.value = true

    try {
      for (const { file, invoiceId } of files) {
        try {
          const result = await analyzeFile(file, invoiceId)
          results.push(result)
        } catch (error) {
          console.error(`Błąd analizy ${file.name}:`, error)
          // Kontynuuj analizę pozostałych plików
        }
      }

      return results

    } finally {
      isAnalyzing.value = false
    }
  }

  function getProgress(id: string): number {
    const progress = progressTracker.getProgress(id)
    return progress?.overallProgress || 0
  }

  function getProgressDetails(id: string) {
    return progressTracker.getProgress(id)
  }

  function getCurrentStage(id: string) {
    return progressTracker.getCurrentStageInfo(id)
  }

  function getError(id: string): string | null {
    return analysisErrors.value[id] || null
  }

  function clearProgress(id: string) {
    progressTracker.removeProgress(id)
    delete analysisErrors.value[id]
  }

  function clearAllProgress() {
    Object.keys(progressTracker.progressState.value).forEach(id => {
      progressTracker.removeProgress(id)
    })
    analysisErrors.value = {}
    isAnalyzing.value = false
    currentAnalysisId.value = null
  }

  return {
    // State
    isAnalyzing: computed(() => isAnalyzing.value),
    analysisErrors: computed(() => analysisErrors.value),
    currentAnalysisId: computed(() => currentAnalysisId.value),
    hasActiveAnalysis,

    // Methods
    analyzeFile,
    analyzeBatch,
    getProgress,
    getProgressDetails,
    getCurrentStage,
    getError,
    clearProgress,
    clearAllProgress
  }
}