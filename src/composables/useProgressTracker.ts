import { ref, computed } from 'vue'

export interface ProgressStage {
  id: string
  name: string
  description: string
  startPercent: number
  endPercent: number
  status: 'pending' | 'active' | 'completed' | 'error'
}

export interface ProgressState {
  currentStage: number
  overallProgress: number
  stages: ProgressStage[]
  isActive: boolean
  error?: string
}

export function useProgressTracker() {
  const progressState = ref<Record<string, ProgressState>>({})

  const createProgressTracker = (
    id: string,
    stages: Omit<ProgressStage, 'status'>[]
  ): ProgressState => {
    const initialStages: ProgressStage[] = stages.map((stage, index) => ({
      ...stage,
      status: index === 0 ? 'pending' : 'pending'
    }))

    const state: ProgressState = {
      currentStage: 0,
      overallProgress: 0,
      stages: initialStages,
      isActive: false,
      error: undefined
    }

    progressState.value[id] = state
    return state
  }

  const startProgress = (id: string) => {
    const state = progressState.value[id]
    if (!state) return

    state.isActive = true
    state.currentStage = 0
    state.overallProgress = 0
    state.error = undefined
    state.stages[0].status = 'active'
  }

  const updateStageProgress = (
    id: string, 
    stageIndex: number, 
    progress: number // 0-100% within this stage
  ) => {
    const state = progressState.value[id]
    if (!state || stageIndex >= state.stages.length) return

    const stage = state.stages[stageIndex]
    const stageRange = stage.endPercent - stage.startPercent
    const stageProgress = (progress / 100) * stageRange
    
    state.overallProgress = stage.startPercent + stageProgress
    
    // Ensure we don't exceed the stage end
    if (state.overallProgress > stage.endPercent) {
      state.overallProgress = stage.endPercent
    }
  }

  const completeStage = (id: string, stageIndex: number) => {
    const state = progressState.value[id]
    if (!state || stageIndex >= state.stages.length) return

    // Mark current stage as completed
    state.stages[stageIndex].status = 'completed'
    state.overallProgress = state.stages[stageIndex].endPercent

    // Move to next stage if available
    if (stageIndex < state.stages.length - 1) {
      state.currentStage = stageIndex + 1
      state.stages[stageIndex + 1].status = 'active'
    }
  }

  const setError = (id: string, error: string) => {
    const state = progressState.value[id]
    if (!state) return

    state.error = error
    state.isActive = false
    
    // Mark current stage as error
    if (state.currentStage < state.stages.length) {
      state.stages[state.currentStage].status = 'error'
    }
  }

  const completeProgress = (id: string) => {
    const state = progressState.value[id]
    if (!state) return

    state.overallProgress = 100
    state.isActive = false
    state.currentStage = state.stages.length - 1
    
    // Mark all stages as completed
    state.stages.forEach(stage => {
      if (stage.status !== 'error') {
        stage.status = 'completed'
      }
    })
  }

  const getProgress = (id: string) => {
    return progressState.value[id] || null
  }

  const removeProgress = (id: string) => {
    delete progressState.value[id]
  }

  const getCurrentStageInfo = (id: string) => {
    const state = progressState.value[id]
    if (!state) return null

    const currentStage = state.stages[state.currentStage]
    return {
      ...currentStage,
      stageProgress: state.currentStage < state.stages.length 
        ? Math.round(((state.overallProgress - currentStage.startPercent) / (currentStage.endPercent - currentStage.startPercent)) * 100)
        : 100
    }
  }

  return {
    progressState: computed(() => progressState.value),
    createProgressTracker,
    startProgress,
    updateStageProgress,
    completeStage,
    setError,
    completeProgress,
    getProgress,
    removeProgress,
    getCurrentStageInfo
  }
}

// Predefined stages for invoice analysis
export const INVOICE_ANALYSIS_STAGES = [
  {
    id: 'upload',
    name: 'Upload pliku',
    description: 'Przesyłanie pliku na serwer',
    startPercent: 0,
    endPercent: 15
  },
  {
    id: 'convert',
    name: 'Konwersja',
    description: 'Konwersja pliku do base64',
    startPercent: 15,
    endPercent: 25
  },
  {
    id: 'send',
    name: 'Wysyłka',
    description: 'Wysyłanie do Gemini API',
    startPercent: 25,
    endPercent: 35
  },
  {
    id: 'analyze',
    name: 'Analiza AI',
    description: 'Przetwarzanie przez sztuczną inteligencję',
    startPercent: 35,
    endPercent: 85
  },
  {
    id: 'parse',
    name: 'Przetwarzanie',
    description: 'Parsowanie i walidacja wyników',
    startPercent: 85,
    endPercent: 95
  },
  {
    id: 'complete',
    name: 'Zakończenie',
    description: 'Zapisywanie wyników',
    startPercent: 95,
    endPercent: 100
  }
]