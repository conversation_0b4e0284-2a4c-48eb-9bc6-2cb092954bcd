import type { AnalysisResult } from '@/types'

const GEMINI_API_URL = 'https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash-exp:generateContent'

export interface GeminiResponse {
  candidates: Array<{
    content: {
      parts: Array<{
        text: string
      }>
    }
  }>
}

const INVOICE_ANALYSIS_PROMPT = `
Przeanalizuj tę fakturę energetyczną i wyciągnij z niej kluczowe informacje. Odpowiedz w formacie JSON zgodnym z następującą strukturą:

{
  "supplier": {
    "name": "nazwa dostawcy energii",
    "address": "adres dostawcy",
    "nip": "NIP dostawcy"
  },
  "customer": {
    "name": "nazwa klienta",
    "address": "adres klienta",
    "accountNumber": "numer konta klienta"
  },
  "invoiceDetails": {
    "number": "numer faktury",
    "issueDate": "data wystawienia (YYYY-MM-DD)",
    "dueDate": "termin pła<PERSON> (YYYY-MM-DD)",
    "serviceDate": "okres rozliczeniowy"
  },
  "energyData": {
    "meterReading": {
      "previous": "poprzedni stan licznika (liczba)",
      "current": "aktualny stan licznika (liczba)",
      "consumption": "zużycie w kWh (liczba)"
    },
    "tariff": "taryfa energetyczna",
    "unitPrice": "cena za kWh (liczba)"
  },
  "financialData": {
    "netAmount": "kwota netto (liczba)",
    "vatAmount": "kwota VAT (liczba)",
    "grossAmount": "kwota brutto (liczba)",
    "currency": "waluta (PLN/EUR/USD)"
  },
  "additionalInfo": ["dodatkowe informacje jako tablica stringów"]
}

Uwagi:
- Jeśli jakiejś informacji nie ma na fakturze, użyj null
- Wszystkie kwoty podawaj jako liczby (bez waluty)
- Daty w formacie YYYY-MM-DD
- Zwróć tylko sam JSON, bez dodatkowych komentarzy

Faktura do analizy:
`

export async function analyzeInvoice(
  file: File, 
  apiKey: string,
  onProgress?: (progress: number) => void
): Promise<AnalysisResult> {
  if (!apiKey) {
    throw new Error('Brak klucza API Google Gemini')
  }

  try {
    onProgress?.(10)
    
    // Konwertuj plik do base64
    const base64Data = await fileToBase64(file)
    onProgress?.(30)

    const mimeType = file.type || 'application/octet-stream'
    
    const requestBody = {
      contents: [
        {
          parts: [
            {
              text: INVOICE_ANALYSIS_PROMPT
            },
            {
              inline_data: {
                mime_type: mimeType,
                data: base64Data
              }
            }
          ]
        }
      ],
      generationConfig: {
        temperature: 0.1,
        maxOutputTokens: 2048,
      }
    }

    onProgress?.(50)

    const response = await fetch(`${GEMINI_API_URL}?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody)
    })

    onProgress?.(80)

    if (!response.ok) {
      const errorData = await response.json()
      console.error('Gemini API Error:', errorData)
      throw new Error(`Błąd API: ${response.status} - ${errorData.error?.message || 'Nieznany błąd'}`)
    }

    const data: GeminiResponse = await response.json()
    
    if (!data.candidates?.[0]?.content?.parts?.[0]?.text) {
      throw new Error('Brak odpowiedzi z API Gemini')
    }

    const resultText = data.candidates[0].content.parts[0].text
    onProgress?.(90)

    // Spróbuj sparsować JSON
    let analysisResult: AnalysisResult
    try {
      // Usuń ewentualne markdown formatting
      const cleanJson = resultText.replace(/```json\n?|```\n?/g, '').trim()
      analysisResult = JSON.parse(cleanJson)
    } catch (parseError) {
      console.error('JSON Parse Error:', parseError)
      console.error('Raw response:', resultText)
      throw new Error('Nie udało się sparsować odpowiedzi z API. Spróbuj ponownie.')
    }

    onProgress?.(100)
    return analysisResult

  } catch (error) {
    console.error('Error analyzing invoice:', error)
    
    if (error instanceof Error) {
      throw error
    }
    
    throw new Error('Nieoczekiwany błąd podczas analizy faktury')
  }
}

export async function validateApiKey(apiKey: string): Promise<boolean> {
  if (!apiKey) return false

  try {
    const response = await fetch(`${GEMINI_API_URL}?key=${apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        contents: [
          {
            parts: [
              {
                text: "Test connection. Respond with 'OK'."
              }
            ]
          }
        ]
      })
    })

    return response.ok
  } catch (error) {
    console.error('API key validation error:', error)
    return false
  }
}

function fileToBase64(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = () => {
      const result = reader.result as string
      // Usuń prefix "data:mime/type;base64,"
      const base64 = result.split(',')[1]
      resolve(base64)
    }
    
    reader.onerror = () => {
      reject(new Error('Nie udało się odczytać pliku'))
    }
    
    reader.readAsDataURL(file)
  })
}

export function createRetryWrapper<T>(
  fn: () => Promise<T>, 
  maxRetries: number = 3, 
  delay: number = 1000
): Promise<T> {
  return new Promise(async (resolve, reject) => {
    let lastError: Error
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const result = await fn()
        resolve(result)
        return
      } catch (error) {
        lastError = error as Error
        
        if (attempt === maxRetries) {
          break
        }
        
        // Exponential backoff
        const waitTime = delay * Math.pow(2, attempt - 1)
        await new Promise(res => setTimeout(res, waitTime))
      }
    }
    
    reject(lastError!)
  })
}