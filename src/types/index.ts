export interface InvoiceData {
  id: string
  fileName: string
  uploadDate: Date
  analyzed: boolean
  analysisData?: AnalysisResult
}

export interface AnalysisResult {
  supplier: {
    name: string
    address?: string
    nip?: string
  }
  customer: {
    name: string
    address?: string
    accountNumber?: string
  }
  invoiceDetails: {
    number: string
    issueDate: string
    dueDate: string
    serviceDate: string
  }
  energyData: {
    meterReading: {
      previous: number
      current: number
      consumption: number
    }
    tariff: string
    unitPrice: number
  }
  financialData: {
    netAmount: number
    vatAmount: number
    grossAmount: number
    currency: string
  }
  additionalInfo?: string[]
}

export interface ApiKeyStore {
  apiKey: string | null
  isValid: boolean
  lastValidated?: Date
}

export interface UploadState {
  files: File[]
  isUploading: boolean
  progress: number
  error?: string
}

export interface AnalysisState {
  isAnalyzing: boolean
  currentFile?: string
  progress: number
  error?: string
  results: Record<string, AnalysisResult>
}