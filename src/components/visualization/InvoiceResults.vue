<template>
  <div class="space-y-6">
    <div class="flex items-center justify-between">
      <div>
        <h2 class="text-2xl font-bold text-gray-900">Wyniki analizy faktury</h2>
        <p class="text-gray-600">{{ fileName }}</p>
      </div>
      
      <div class="flex space-x-3">
        <button
          @click="copyToClipboard"
          class="btn-secondary"
          :disabled="copyStatus === 'copying'"
        >
          <ClipboardDocumentIcon class="h-4 w-4 mr-2" />
          <span v-if="copyStatus === 'idle'">Ko<PERSON>uj dane</span>
          <span v-else-if="copyStatus === 'copying'">Kopiowanie...</span>
          <span v-else-if="copyStatus === 'success'">Skopiowano!</span>
          <span v-else>Błąd</span>
        </button>
        
        <button
          @click="exportToJSON"
          class="btn-secondary"
        >
          <ArrowDownTrayIcon class="h-4 w-4 mr-2" />
          Eksport JSON
        </button>
        
        <button
          @click="exportToCSV"
          class="btn-secondary"
        >
          <TableCellsIcon class="h-4 w-4 mr-2" />
          Eksport CSV
        </button>
      </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Dostawca -->
      <div class="card">
        <div class="flex items-center mb-4">
          <BuildingOfficeIcon class="h-5 w-5 text-primary-600 mr-2" />
          <h3 class="text-lg font-medium text-gray-900">Dostawca energii</h3>
        </div>
        
        <dl class="space-y-2">
          <div>
            <dt class="text-sm font-medium text-gray-500">Nazwa</dt>
            <dd class="text-sm text-gray-900">{{ data.supplier?.name || 'Brak danych' }}</dd>
          </div>
          <div v-if="data.supplier?.address">
            <dt class="text-sm font-medium text-gray-500">Adres</dt>
            <dd class="text-sm text-gray-900">{{ data.supplier.address }}</dd>
          </div>
          <div v-if="data.supplier?.nip">
            <dt class="text-sm font-medium text-gray-500">NIP</dt>
            <dd class="text-sm text-gray-900">{{ data.supplier.nip }}</dd>
          </div>
        </dl>
      </div>

      <!-- Klient -->
      <div class="card">
        <div class="flex items-center mb-4">
          <UserIcon class="h-5 w-5 text-primary-600 mr-2" />
          <h3 class="text-lg font-medium text-gray-900">Klient</h3>
        </div>
        
        <dl class="space-y-2">
          <div>
            <dt class="text-sm font-medium text-gray-500">Nazwa</dt>
            <dd class="text-sm text-gray-900">{{ data.customer?.name || 'Brak danych' }}</dd>
          </div>
          <div v-if="data.customer?.address">
            <dt class="text-sm font-medium text-gray-500">Adres</dt>
            <dd class="text-sm text-gray-900">{{ data.customer.address }}</dd>
          </div>
          <div v-if="data.customer?.accountNumber">
            <dt class="text-sm font-medium text-gray-500">Numer konta</dt>
            <dd class="text-sm text-gray-900">{{ data.customer.accountNumber }}</dd>
          </div>
        </dl>
      </div>

      <!-- Szczegóły faktury -->
      <div class="card">
        <div class="flex items-center mb-4">
          <DocumentTextIcon class="h-5 w-5 text-primary-600 mr-2" />
          <h3 class="text-lg font-medium text-gray-900">Szczegóły faktury</h3>
        </div>
        
        <dl class="space-y-2">
          <div>
            <dt class="text-sm font-medium text-gray-500">Numer faktury</dt>
            <dd class="text-sm text-gray-900 font-mono">{{ data.invoiceDetails?.number || 'Brak danych' }}</dd>
          </div>
          <div v-if="data.invoiceDetails?.issueDate">
            <dt class="text-sm font-medium text-gray-500">Data wystawienia</dt>
            <dd class="text-sm text-gray-900">{{ formatDate(data.invoiceDetails.issueDate) }}</dd>
          </div>
          <div v-if="data.invoiceDetails?.dueDate">
            <dt class="text-sm font-medium text-gray-500">Termin płatności</dt>
            <dd class="text-sm text-gray-900">{{ formatDate(data.invoiceDetails.dueDate) }}</dd>
          </div>
          <div v-if="data.invoiceDetails?.serviceDate">
            <dt class="text-sm font-medium text-gray-500">Okres rozliczeniowy</dt>
            <dd class="text-sm text-gray-900">{{ data.invoiceDetails.serviceDate }}</dd>
          </div>
        </dl>
      </div>

      <!-- Dane energetyczne -->
      <div class="card">
        <div class="flex items-center mb-4">
          <BoltIcon class="h-5 w-5 text-amber-600 mr-2" />
          <h3 class="text-lg font-medium text-gray-900">Dane energetyczne</h3>
        </div>
        
        <dl class="space-y-2">
          <div v-if="data.energyData?.meterReading">
            <dt class="text-sm font-medium text-gray-500">Stan poprzedni</dt>
            <dd class="text-sm text-gray-900">{{ formatNumber(data.energyData.meterReading.previous) }} kWh</dd>
          </div>
          <div v-if="data.energyData?.meterReading">
            <dt class="text-sm font-medium text-gray-500">Stan aktualny</dt>
            <dd class="text-sm text-gray-900">{{ formatNumber(data.energyData.meterReading.current) }} kWh</dd>
          </div>
          <div v-if="data.energyData?.meterReading">
            <dt class="text-sm font-medium text-gray-500">Zużycie</dt>
            <dd class="text-sm text-gray-900 font-semibold text-lg">{{ formatNumber(data.energyData.meterReading.consumption) }} kWh</dd>
          </div>
          <div v-if="data.energyData?.tariff">
            <dt class="text-sm font-medium text-gray-500">Taryfa</dt>
            <dd class="text-sm text-gray-900">{{ data.energyData.tariff }}</dd>
          </div>
          <div v-if="data.energyData?.unitPrice">
            <dt class="text-sm font-medium text-gray-500">Cena za kWh</dt>
            <dd class="text-sm text-gray-900">{{ formatCurrency(data.energyData.unitPrice) }}</dd>
          </div>
        </dl>
      </div>
    </div>

    <!-- Dane finansowe - pełna szerokość -->
    <div class="card">
      <div class="flex items-center mb-4">
        <CurrencyDollarIcon class="h-5 w-5 text-success-600 mr-2" />
        <h3 class="text-lg font-medium text-gray-900">Dane finansowe</h3>
      </div>
      
      <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="text-center">
          <dt class="text-sm font-medium text-gray-500">Kwota netto</dt>
          <dd class="text-lg font-semibold text-gray-900 mt-1">
            {{ formatCurrency(data.financialData?.netAmount) }}
          </dd>
        </div>
        <div class="text-center">
          <dt class="text-sm font-medium text-gray-500">VAT</dt>
          <dd class="text-lg font-semibold text-gray-900 mt-1">
            {{ formatCurrency(data.financialData?.vatAmount) }}
          </dd>
        </div>
        <div class="text-center">
          <dt class="text-sm font-medium text-gray-500">Kwota brutto</dt>
          <dd class="text-xl font-bold text-primary-600 mt-1">
            {{ formatCurrency(data.financialData?.grossAmount) }}
          </dd>
        </div>
        <div class="text-center">
          <dt class="text-sm font-medium text-gray-500">Waluta</dt>
          <dd class="text-lg font-semibold text-gray-900 mt-1">
            {{ data.financialData?.currency || 'PLN' }}
          </dd>
        </div>
      </div>
    </div>

    <!-- Dodatkowe informacje -->
    <div v-if="data.additionalInfo?.length" class="card">
      <div class="flex items-center mb-4">
        <InformationCircleIcon class="h-5 w-5 text-blue-600 mr-2" />
        <h3 class="text-lg font-medium text-gray-900">Dodatkowe informacje</h3>
      </div>
      
      <ul class="space-y-1">
        <li
          v-for="(info, index) in data.additionalInfo"
          :key="index"
          class="text-sm text-gray-600 flex items-start"
        >
          <span class="w-2 h-2 bg-blue-400 rounded-full mt-1.5 mr-2 flex-shrink-0"></span>
          {{ info }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  BuildingOfficeIcon,
  UserIcon,
  DocumentTextIcon,
  BoltIcon,
  CurrencyDollarIcon,
  InformationCircleIcon,
  ClipboardDocumentIcon,
  ArrowDownTrayIcon,
  TableCellsIcon
} from '@heroicons/vue/24/outline'
import type { AnalysisResult } from '@/types'

interface Props {
  data: AnalysisResult
  fileName?: string
}

const props = defineProps<Props>()

const copyStatus = ref<'idle' | 'copying' | 'success' | 'error'>('idle')

const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return 'Brak danych'
  
  try {
    return new Date(dateString).toLocaleDateString('pl-PL', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  } catch {
    return dateString
  }
}

const formatNumber = (num: number | null | undefined): string => {
  if (num === null || num === undefined) return 'Brak danych'
  return new Intl.NumberFormat('pl-PL').format(num)
}

const formatCurrency = (amount: number | null | undefined, currency: string = 'PLN'): string => {
  if (amount === null || amount === undefined) return 'Brak danych'
  
  return new Intl.NumberFormat('pl-PL', {
    style: 'currency',
    currency: currency
  }).format(amount)
}

async function copyToClipboard() {
  copyStatus.value = 'copying'
  
  try {
    const textData = generateTextSummary()
    await navigator.clipboard.writeText(textData)
    copyStatus.value = 'success'
    
    setTimeout(() => {
      copyStatus.value = 'idle'
    }, 2000)
  } catch (error) {
    console.error('Failed to copy:', error)
    copyStatus.value = 'error'
    
    setTimeout(() => {
      copyStatus.value = 'idle'
    }, 2000)
  }
}

function generateTextSummary(): string {
  const sections = [
    `ANALIZA FAKTURY ENERGETYCZNEJ`,
    `${props.fileName ? `Plik: ${props.fileName}` : ''}`,
    ``,
    `DOSTAWCA:`,
    `Nazwa: ${props.data.supplier?.name || 'Brak danych'}`,
    `${props.data.supplier?.address ? `Adres: ${props.data.supplier.address}` : ''}`,
    `${props.data.supplier?.nip ? `NIP: ${props.data.supplier.nip}` : ''}`,
    ``,
    `KLIENT:`,
    `Nazwa: ${props.data.customer?.name || 'Brak danych'}`,
    `${props.data.customer?.address ? `Adres: ${props.data.customer.address}` : ''}`,
    `${props.data.customer?.accountNumber ? `Numer konta: ${props.data.customer.accountNumber}` : ''}`,
    ``,
    `FAKTURA:`,
    `Numer: ${props.data.invoiceDetails?.number || 'Brak danych'}`,
    `${props.data.invoiceDetails?.issueDate ? `Data wystawienia: ${formatDate(props.data.invoiceDetails.issueDate)}` : ''}`,
    `${props.data.invoiceDetails?.dueDate ? `Termin płatności: ${formatDate(props.data.invoiceDetails.dueDate)}` : ''}`,
    `${props.data.invoiceDetails?.serviceDate ? `Okres: ${props.data.invoiceDetails.serviceDate}` : ''}`,
    ``,
    `ENERGIA:`,
    `${props.data.energyData?.meterReading ? `Zużycie: ${formatNumber(props.data.energyData.meterReading.consumption)} kWh` : ''}`,
    `${props.data.energyData?.unitPrice ? `Cena za kWh: ${formatCurrency(props.data.energyData.unitPrice)}` : ''}`,
    ``,
    `KWOTY:`,
    `${props.data.financialData?.netAmount ? `Netto: ${formatCurrency(props.data.financialData.netAmount)}` : ''}`,
    `${props.data.financialData?.vatAmount ? `VAT: ${formatCurrency(props.data.financialData.vatAmount)}` : ''}`,
    `${props.data.financialData?.grossAmount ? `Brutto: ${formatCurrency(props.data.financialData.grossAmount)}` : ''}`,
  ]
  
  return sections.filter(line => line !== '').join('\n')
}

function exportToJSON() {
  const dataStr = JSON.stringify(props.data, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  
  const link = document.createElement('a')
  link.href = URL.createObjectURL(dataBlob)
  link.download = `invoice-analysis-${props.data.invoiceDetails?.number || 'unknown'}-${Date.now()}.json`
  link.click()
  
  URL.revokeObjectURL(link.href)
}

function exportToCSV() {
  const rows = [
    ['Kategoria', 'Pole', 'Wartość'],
    ['Dostawca', 'Nazwa', props.data.supplier?.name || ''],
    ['Dostawca', 'Adres', props.data.supplier?.address || ''],
    ['Dostawca', 'NIP', props.data.supplier?.nip || ''],
    ['Klient', 'Nazwa', props.data.customer?.name || ''],
    ['Klient', 'Adres', props.data.customer?.address || ''],
    ['Klient', 'Numer konta', props.data.customer?.accountNumber || ''],
    ['Faktura', 'Numer', props.data.invoiceDetails?.number || ''],
    ['Faktura', 'Data wystawienia', props.data.invoiceDetails?.issueDate || ''],
    ['Faktura', 'Termin płatności', props.data.invoiceDetails?.dueDate || ''],
    ['Faktura', 'Okres rozliczeniowy', props.data.invoiceDetails?.serviceDate || ''],
    ['Energia', 'Stan poprzedni', props.data.energyData?.meterReading?.previous?.toString() || ''],
    ['Energia', 'Stan aktualny', props.data.energyData?.meterReading?.current?.toString() || ''],
    ['Energia', 'Zużycie (kWh)', props.data.energyData?.meterReading?.consumption?.toString() || ''],
    ['Energia', 'Taryfa', props.data.energyData?.tariff || ''],
    ['Energia', 'Cena za kWh', props.data.energyData?.unitPrice?.toString() || ''],
    ['Finanse', 'Kwota netto', props.data.financialData?.netAmount?.toString() || ''],
    ['Finanse', 'VAT', props.data.financialData?.vatAmount?.toString() || ''],
    ['Finanse', 'Kwota brutto', props.data.financialData?.grossAmount?.toString() || ''],
    ['Finanse', 'Waluta', props.data.financialData?.currency || ''],
  ]
  
  const csvContent = rows.map(row => 
    row.map(field => `"${field}"`).join(',')
  ).join('\n')
  
  const dataBlob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  
  const link = document.createElement('a')
  link.href = URL.createObjectURL(dataBlob)
  link.download = `invoice-analysis-${props.data.invoiceDetails?.number || 'unknown'}-${Date.now()}.csv`
  link.click()
  
  URL.revokeObjectURL(link.href)
}
</script>