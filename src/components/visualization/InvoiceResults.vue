<template>
  <div class="space-y-6">
    <!-- P<PERSON>yciski eksportu -->
    <div class="flex flex-col sm:flex-row gap-3 justify-end">
      <button
        @click="copyToClipboard"
        class="btn-secondary"
        :disabled="copyStatus === 'copying'"
      >
        <ClipboardDocumentIcon class="h-4 w-4 mr-2" />
        <span v-if="copyStatus === 'idle'"><PERSON><PERSON><PERSON><PERSON> dane</span>
        <span v-else-if="copyStatus === 'copying'">Kopiowanie...</span>
        <span v-else-if="copyStatus === 'success'">Skopiowano!</span>
        <span v-else>Błąd</span>
      </button>

      <button
        @click="exportToJSON"
        class="btn-secondary"
      >
        <ArrowDownTrayIcon class="h-4 w-4 mr-2" />
        Eksport JSON
      </button>

      <button
        @click="exportToCSV"
        class="btn-secondary"
      >
        <TableCellsIcon class="h-4 w-4 mr-2" />
        Eksport CSV
      </button>
    </div>

    <!-- <PERSON> finansowe - kompaktowe -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      <div class="text-center p-4 bg-neutral-50 rounded-lg border border-neutral-200">
        <dt class="text-caption font-semibold text-neutral-600 mb-2">Kwota netto</dt>
        <dd class="text-heading-3 text-neutral-900">
          {{ formatCurrency(data.financialData?.netAmount) }}
        </dd>
      </div>
      <div class="text-center p-4 bg-neutral-50 rounded-lg border border-neutral-200">
        <dt class="text-caption font-semibold text-neutral-600 mb-2">VAT</dt>
        <dd class="text-heading-3 text-neutral-900">
          {{ formatCurrency(data.financialData?.vatAmount) }}
        </dd>
      </div>
      <div class="text-center p-4 bg-success-50 rounded-lg border border-success-200">
        <dt class="text-caption font-semibold text-success-700 mb-2">Kwota brutto</dt>
        <dd class="text-heading-1 text-success-700">
          {{ formatCurrency(data.financialData?.grossAmount) }}
        </dd>
      </div>
      <div class="text-center p-4 bg-neutral-50 rounded-lg border border-neutral-200">
        <dt class="text-caption font-semibold text-neutral-600 mb-2">Waluta</dt>
        <dd class="text-heading-3 text-neutral-900">
          {{ data.financialData?.currency || 'PLN' }}
        </dd>
      </div>
    </div>

    <!-- Szczegóły faktury -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- Dostawca -->
      <div class="card">
        <div class="flex items-center mb-4">
          <div class="flex items-center justify-center w-8 h-8 bg-accent-100 rounded-lg mr-3">
            <BuildingOfficeIcon class="h-4 w-4 text-accent-600" />
          </div>
          <h3 class="text-heading-3">Dostawca energii</h3>
        </div>

        <dl class="space-y-3">
          <div>
            <dt class="text-caption font-semibold text-neutral-600 mb-1">Nazwa</dt>
            <dd class="text-body font-medium text-neutral-900">{{ data.supplier?.name || 'Brak danych' }}</dd>
          </div>
          <div v-if="data.supplier?.address">
            <dt class="text-caption font-semibold text-neutral-600 mb-1">Adres</dt>
            <dd class="text-body text-neutral-700">{{ data.supplier.address }}</dd>
          </div>
          <div v-if="data.supplier?.nip">
            <dt class="text-caption font-semibold text-neutral-600 mb-1">NIP</dt>
            <dd class="text-body font-mono text-neutral-700">{{ data.supplier.nip }}</dd>
          </div>
        </dl>
      </div>

      <!-- Klient -->
      <div class="card">
        <div class="flex items-center mb-4">
          <div class="flex items-center justify-center w-8 h-8 bg-success-100 rounded-lg mr-3">
            <UserIcon class="h-4 w-4 text-success-600" />
          </div>
          <h3 class="text-heading-3">Klient</h3>
        </div>

        <dl class="space-y-3">
          <div>
            <dt class="text-caption font-semibold text-neutral-600 mb-1">Nazwa</dt>
            <dd class="text-body font-medium text-neutral-900">{{ data.customer?.name || 'Brak danych' }}</dd>
          </div>
          <div v-if="data.customer?.address">
            <dt class="text-caption font-semibold text-neutral-600 mb-1">Adres</dt>
            <dd class="text-body text-neutral-700">{{ data.customer.address }}</dd>
          </div>
          <div v-if="data.customer?.accountNumber">
            <dt class="text-caption font-semibold text-neutral-600 mb-1">Numer konta</dt>
            <dd class="text-body font-mono text-neutral-700">{{ data.customer.accountNumber }}</dd>
          </div>
        </dl>
      </div>

      <!-- Szczegóły faktury -->
      <div class="card">
        <div class="flex items-center mb-4">
          <div class="flex items-center justify-center w-8 h-8 bg-warning-100 rounded-lg mr-3">
            <DocumentTextIcon class="h-4 w-4 text-warning-600" />
          </div>
          <h3 class="text-heading-3">Szczegóły faktury</h3>
        </div>

        <dl class="space-y-3">
          <div>
            <dt class="text-caption font-semibold text-neutral-600 mb-1">Numer faktury</dt>
            <dd class="text-body font-mono font-medium text-neutral-900">{{ data.invoiceDetails?.number || 'Brak danych' }}</dd>
          </div>
          <div v-if="data.invoiceDetails?.issueDate">
            <dt class="text-caption font-semibold text-neutral-600 mb-1">Data wystawienia</dt>
            <dd class="text-body text-neutral-700">{{ formatDate(data.invoiceDetails.issueDate) }}</dd>
          </div>
          <div v-if="data.invoiceDetails?.dueDate">
            <dt class="text-caption font-semibold text-neutral-600 mb-1">Termin płatności</dt>
            <dd class="text-body text-neutral-700">{{ formatDate(data.invoiceDetails.dueDate) }}</dd>
          </div>
          <div v-if="data.invoiceDetails?.serviceDate">
            <dt class="text-caption font-semibold text-neutral-600 mb-1">Okres rozliczeniowy</dt>
            <dd class="text-body text-neutral-700">{{ data.invoiceDetails.serviceDate }}</dd>
          </div>
        </dl>
      </div>

      <!-- Dane energetyczne -->
      <div class="card">
        <div class="flex items-center mb-4">
          <div class="flex items-center justify-center w-8 h-8 bg-warning-100 rounded-lg mr-3">
            <BoltIcon class="h-4 w-4 text-warning-600" />
          </div>
          <h3 class="text-heading-3">Dane energetyczne</h3>
        </div>

        <dl class="space-y-3">
          <div v-if="data.energyData?.meterReading">
            <dt class="text-caption font-semibold text-neutral-600 mb-1">Stan poprzedni</dt>
            <dd class="text-body text-neutral-700">{{ formatNumber(data.energyData.meterReading.previous) }} kWh</dd>
          </div>
          <div v-if="data.energyData?.meterReading">
            <dt class="text-caption font-semibold text-neutral-600 mb-1">Stan aktualny</dt>
            <dd class="text-body text-neutral-700">{{ formatNumber(data.energyData.meterReading.current) }} kWh</dd>
          </div>
          <div v-if="data.energyData?.meterReading">
            <dt class="text-caption font-semibold text-neutral-600 mb-1">Zużycie</dt>
            <dd class="text-heading-3 text-accent-700">{{ formatNumber(data.energyData.meterReading.consumption) }} kWh</dd>
          </div>
          <div v-if="data.energyData?.tariff">
            <dt class="text-caption font-semibold text-neutral-600 mb-1">Taryfa</dt>
            <dd class="text-body text-neutral-700">{{ data.energyData.tariff }}</dd>
          </div>
          <div v-if="data.energyData?.unitPrice">
            <dt class="text-caption font-semibold text-neutral-600 mb-1">Cena za kWh</dt>
            <dd class="text-body font-medium text-neutral-900">{{ formatCurrency(data.energyData.unitPrice) }}</dd>
          </div>
        </dl>
      </div>
    </div>

    <!-- Dodatkowe informacje -->
    <div v-if="data.additionalInfo?.length" class="card">
      <div class="flex items-center mb-4">
        <div class="flex items-center justify-center w-8 h-8 bg-blue-100 rounded-lg mr-3">
          <InformationCircleIcon class="h-4 w-4 text-blue-600" />
        </div>
        <h3 class="text-heading-3">Dodatkowe informacje</h3>
      </div>

      <ul class="space-y-2">
        <li
          v-for="(info, index) in data.additionalInfo"
          :key="index"
          class="text-body text-neutral-700 flex items-start"
        >
          <span class="w-2 h-2 bg-blue-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
          {{ info }}
        </li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  BuildingOfficeIcon,
  UserIcon,
  DocumentTextIcon,
  BoltIcon,
  CurrencyDollarIcon,
  InformationCircleIcon,
  ClipboardDocumentIcon,
  ArrowDownTrayIcon,
  TableCellsIcon
} from '@heroicons/vue/24/outline'
import type { AnalysisResult } from '@/types'

interface Props {
  data: AnalysisResult
  fileName?: string
}

const props = defineProps<Props>()

const copyStatus = ref<'idle' | 'copying' | 'success' | 'error'>('idle')

const formatDate = (dateString: string | null | undefined): string => {
  if (!dateString) return 'Brak danych'
  
  try {
    return new Date(dateString).toLocaleDateString('pl-PL', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  } catch {
    return dateString
  }
}

const formatNumber = (num: number | null | undefined): string => {
  if (num === null || num === undefined) return 'Brak danych'
  return new Intl.NumberFormat('pl-PL').format(num)
}

const formatCurrency = (amount: number | null | undefined, currency: string = 'PLN'): string => {
  if (amount === null || amount === undefined) return 'Brak danych'
  
  return new Intl.NumberFormat('pl-PL', {
    style: 'currency',
    currency: currency
  }).format(amount)
}

async function copyToClipboard() {
  copyStatus.value = 'copying'
  
  try {
    const textData = generateTextSummary()
    await navigator.clipboard.writeText(textData)
    copyStatus.value = 'success'
    
    setTimeout(() => {
      copyStatus.value = 'idle'
    }, 2000)
  } catch (error) {
    console.error('Failed to copy:', error)
    copyStatus.value = 'error'
    
    setTimeout(() => {
      copyStatus.value = 'idle'
    }, 2000)
  }
}

function generateTextSummary(): string {
  const sections = [
    `ANALIZA FAKTURY ENERGETYCZNEJ`,
    `${props.fileName ? `Plik: ${props.fileName}` : ''}`,
    ``,
    `DOSTAWCA:`,
    `Nazwa: ${props.data.supplier?.name || 'Brak danych'}`,
    `${props.data.supplier?.address ? `Adres: ${props.data.supplier.address}` : ''}`,
    `${props.data.supplier?.nip ? `NIP: ${props.data.supplier.nip}` : ''}`,
    ``,
    `KLIENT:`,
    `Nazwa: ${props.data.customer?.name || 'Brak danych'}`,
    `${props.data.customer?.address ? `Adres: ${props.data.customer.address}` : ''}`,
    `${props.data.customer?.accountNumber ? `Numer konta: ${props.data.customer.accountNumber}` : ''}`,
    ``,
    `FAKTURA:`,
    `Numer: ${props.data.invoiceDetails?.number || 'Brak danych'}`,
    `${props.data.invoiceDetails?.issueDate ? `Data wystawienia: ${formatDate(props.data.invoiceDetails.issueDate)}` : ''}`,
    `${props.data.invoiceDetails?.dueDate ? `Termin płatności: ${formatDate(props.data.invoiceDetails.dueDate)}` : ''}`,
    `${props.data.invoiceDetails?.serviceDate ? `Okres: ${props.data.invoiceDetails.serviceDate}` : ''}`,
    ``,
    `ENERGIA:`,
    `${props.data.energyData?.meterReading ? `Zużycie: ${formatNumber(props.data.energyData.meterReading.consumption)} kWh` : ''}`,
    `${props.data.energyData?.unitPrice ? `Cena za kWh: ${formatCurrency(props.data.energyData.unitPrice)}` : ''}`,
    ``,
    `KWOTY:`,
    `${props.data.financialData?.netAmount ? `Netto: ${formatCurrency(props.data.financialData.netAmount)}` : ''}`,
    `${props.data.financialData?.vatAmount ? `VAT: ${formatCurrency(props.data.financialData.vatAmount)}` : ''}`,
    `${props.data.financialData?.grossAmount ? `Brutto: ${formatCurrency(props.data.financialData.grossAmount)}` : ''}`,
  ]
  
  return sections.filter(line => line !== '').join('\n')
}

function exportToJSON() {
  const dataStr = JSON.stringify(props.data, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  
  const link = document.createElement('a')
  link.href = URL.createObjectURL(dataBlob)
  link.download = `invoice-analysis-${props.data.invoiceDetails?.number || 'unknown'}-${Date.now()}.json`
  link.click()
  
  URL.revokeObjectURL(link.href)
}

function exportToCSV() {
  const rows = [
    ['Kategoria', 'Pole', 'Wartość'],
    ['Dostawca', 'Nazwa', props.data.supplier?.name || ''],
    ['Dostawca', 'Adres', props.data.supplier?.address || ''],
    ['Dostawca', 'NIP', props.data.supplier?.nip || ''],
    ['Klient', 'Nazwa', props.data.customer?.name || ''],
    ['Klient', 'Adres', props.data.customer?.address || ''],
    ['Klient', 'Numer konta', props.data.customer?.accountNumber || ''],
    ['Faktura', 'Numer', props.data.invoiceDetails?.number || ''],
    ['Faktura', 'Data wystawienia', props.data.invoiceDetails?.issueDate || ''],
    ['Faktura', 'Termin płatności', props.data.invoiceDetails?.dueDate || ''],
    ['Faktura', 'Okres rozliczeniowy', props.data.invoiceDetails?.serviceDate || ''],
    ['Energia', 'Stan poprzedni', props.data.energyData?.meterReading?.previous?.toString() || ''],
    ['Energia', 'Stan aktualny', props.data.energyData?.meterReading?.current?.toString() || ''],
    ['Energia', 'Zużycie (kWh)', props.data.energyData?.meterReading?.consumption?.toString() || ''],
    ['Energia', 'Taryfa', props.data.energyData?.tariff || ''],
    ['Energia', 'Cena za kWh', props.data.energyData?.unitPrice?.toString() || ''],
    ['Finanse', 'Kwota netto', props.data.financialData?.netAmount?.toString() || ''],
    ['Finanse', 'VAT', props.data.financialData?.vatAmount?.toString() || ''],
    ['Finanse', 'Kwota brutto', props.data.financialData?.grossAmount?.toString() || ''],
    ['Finanse', 'Waluta', props.data.financialData?.currency || ''],
  ]
  
  const csvContent = rows.map(row => 
    row.map(field => `"${field}"`).join(',')
  ).join('\n')
  
  const dataBlob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
  
  const link = document.createElement('a')
  link.href = URL.createObjectURL(dataBlob)
  link.download = `invoice-analysis-${props.data.invoiceDetails?.number || 'unknown'}-${Date.now()}.csv`
  link.click()
  
  URL.revokeObjectURL(link.href)
}
</script>