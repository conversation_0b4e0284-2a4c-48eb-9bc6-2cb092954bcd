<template>
  <div class="bg-white rounded-lg border border-gray-200 p-4">
    <div class="flex items-center justify-between mb-3">
      <h3 class="text-sm font-medium text-gray-900">
        {{ fileName || 'Przetwarzanie pliku' }}
      </h3>
      <span class="text-sm text-gray-500">
        {{ Math.round(progress?.overallProgress || 0) }}%
      </span>
    </div>

    <!-- Overall progress bar -->
    <div class="w-full bg-gray-200 rounded-full h-2 mb-4">
      <div
        class="bg-primary-600 h-2 rounded-full transition-all duration-300"
        :style="{ width: `${progress?.overallProgress || 0}%` }"
      ></div>
    </div>

    <!-- Current stage info -->
    <div v-if="currentStage" class="flex items-center space-x-3">
      <div
        :class="[
          'flex-shrink-0 w-3 h-3 rounded-full',
          getStageStatusColor(currentStage.status)
        ]"
      >
        <CheckIcon
          v-if="currentStage.status === 'completed'"
          class="w-3 h-3 text-white"
        />
        <ExclamationCircleIcon
          v-else-if="currentStage.status === 'error'"
          class="w-3 h-3 text-white"
        />
        <div
          v-else-if="currentStage.status === 'active'"
          class="w-full h-full animate-pulse bg-current rounded-full"
        ></div>
      </div>
      
      <div class="min-w-0 flex-1">
        <p class="text-sm font-medium text-gray-900">
          {{ currentStage.name }}
        </p>
        <p class="text-xs text-gray-500">
          {{ currentStage.description }}
        </p>
      </div>
    </div>

    <!-- Detailed stages (optional) -->
    <div v-if="showDetails && progress?.stages" class="mt-4 space-y-2">
      <div
        v-for="(stage, index) in progress.stages"
        :key="stage.id"
        class="flex items-center space-x-3"
      >
        <div
          :class="[
            'flex-shrink-0 w-2 h-2 rounded-full',
            getStageStatusColor(stage.status)
          ]"
        ></div>
        
        <div class="min-w-0 flex-1">
          <div class="flex items-center justify-between">
            <p class="text-xs font-medium text-gray-700">
              {{ stage.name }}
            </p>
            <span class="text-xs text-gray-500">
              {{ stage.startPercent }}%-{{ stage.endPercent }}%
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Error message -->
    <div v-if="progress?.error" class="mt-3 p-2 bg-error-50 rounded-md">
      <div class="flex">
        <ExclamationCircleIcon class="h-4 w-4 text-error-400 mr-2" />
        <p class="text-xs text-error-800">
          {{ progress.error }}
        </p>
      </div>
    </div>

    <!-- Actions -->
    <div v-if="showActions" class="mt-4 flex justify-end space-x-2">
      <button
        v-if="progress?.error"
        @click="$emit('retry')"
        class="text-xs btn-secondary"
      >
        Spróbuj ponownie
      </button>
      
      <button
        @click="$emit('cancel')"
        class="text-xs btn-secondary"
        :disabled="!progress?.isActive"
      >
        Anuluj
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  CheckIcon,
  ExclamationCircleIcon
} from '@heroicons/vue/24/outline'
import type { ProgressState } from '@/composables/useProgressTracker'

interface Props {
  progress: ProgressState | null
  fileName?: string
  showDetails?: boolean
  showActions?: boolean
}

interface Emits {
  (e: 'retry'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  showDetails: false,
  showActions: false
})

defineEmits<Emits>()

const currentStage = computed(() => {
  if (!props.progress) return null
  
  const stage = props.progress.stages[props.progress.currentStage]
  if (!stage) return null
  
  return {
    ...stage,
    stageProgress: props.progress.currentStage < props.progress.stages.length 
      ? Math.round(((props.progress.overallProgress - stage.startPercent) / (stage.endPercent - stage.startPercent)) * 100)
      : 100
  }
})

function getStageStatusColor(status: string): string {
  switch (status) {
    case 'completed':
      return 'bg-success-500'
    case 'active':
      return 'bg-primary-500'
    case 'error':
      return 'bg-error-500'
    case 'pending':
    default:
      return 'bg-gray-300'
  }
}
</script>