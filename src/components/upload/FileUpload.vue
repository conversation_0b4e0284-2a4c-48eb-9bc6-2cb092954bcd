<template>
  <div class="space-y-6">
    <div
      @drop="handleDrop"
      @dragover="handleDragOver"
      @dragenter="handleDragEnter"
      @dragleave="handleDragLeave"
      :class="[
        'relative border-2 border-dashed rounded-lg p-8 text-center transition-colors',
        isDragOver
          ? 'border-primary-300 bg-primary-50'
          : 'border-gray-300 bg-white hover:border-gray-400'
      ]"
    >
      <CloudArrowUpIcon class="mx-auto h-12 w-12 text-gray-400" />
      <div class="mt-4">
        <h3 class="text-lg font-medium text-gray-900">
          Przeciągnij i upuść faktury tutaj
        </h3>
        <p class="mt-2 text-sm text-gray-600">
          lub
        </p>
        <button
          @click="triggerFileInput"
          class="mt-2 btn-primary"
          :disabled="isUploading"
        >
          Wybierz pliki
        </button>
      </div>
      <p class="mt-4 text-xs text-gray-500">
        Obsługiwane formaty: PDF, JPG, PNG, WebP (max 10MB)
      </p>
      
      <input
        ref="fileInput"
        type="file"
        multiple
        accept=".pdf,.jpg,.jpeg,.png,.webp"
        class="hidden"
        @change="handleFileSelect"
      />
    </div>

    <div v-if="selectedFiles.length > 0" class="space-y-4">
      <h4 class="font-medium text-gray-900">Wybrane pliki ({{ selectedFiles.length }})</h4>
      
      <div class="space-y-2">
        <div
          v-for="(file, index) in selectedFiles"
          :key="index"
          class="flex items-center justify-between p-3 bg-gray-50 rounded-md"
        >
          <div class="flex items-center space-x-3">
            <DocumentTextIcon class="h-5 w-5 text-gray-400" />
            <div>
              <p class="text-sm font-medium text-gray-900">{{ file.name }}</p>
              <p class="text-xs text-gray-500">
                {{ formatFileSize(file.size) }} • {{ file.type || 'Nieznany typ' }}
              </p>
            </div>
          </div>
          
          <button
            @click="removeFile(index)"
            :disabled="isUploading"
            class="text-gray-400 hover:text-red-500 disabled:opacity-50"
          >
            <XMarkIcon class="h-5 w-5" />
          </button>
        </div>
      </div>

      <div class="space-y-3">
        <div class="flex justify-between">
          <div class="flex space-x-3">
            <button
              @click="uploadFiles"
              :disabled="isUploading || selectedFiles.length === 0"
              class="btn-primary"
            >
              <span v-if="isUploading">Przesyłanie...</span>
              <span v-else-if="selectedFiles.length === 1">Prześlij i przejdź do wyników</span>
              <span v-else>Prześlij i analizuj ({{ selectedFiles.length }})</span>
            </button>
            
            <button
              @click="clearFiles"
              :disabled="isUploading"
              class="btn-secondary"
            >
              Wyczyść listę
            </button>
          </div>
        </div>
        
        <div v-if="selectedFiles.length > 0" class="text-xs text-gray-500">
          <span v-if="selectedFiles.length === 1">
            💡 Po analizie zostaniesz automatycznie przekierowany do wyników
          </span>
          <span v-else>
            💡 Analiza rozpocznie się natychmiast, zostaniesz przekierowany do historii
          </span>
        </div>
      </div>
    </div>

    <div v-if="error" class="p-4 bg-error-50 rounded-md">
      <div class="flex">
        <ExclamationTriangleIcon class="h-5 w-5 text-error-400 mr-3" />
        <div class="text-sm text-error-800">
          {{ error }}
        </div>
      </div>
    </div>

    <!-- Active analyses -->
    <div v-if="Object.keys(activeAnalyses).length > 0" class="space-y-4">
      <h4 class="font-medium text-gray-900">Przetwarzanie w toku</h4>
      
      <div class="space-y-3">
        <ProgressIndicator
          v-for="(analysis, trackerId) in activeAnalyses"
          :key="trackerId"
          :progress="getProgressDetails(trackerId)"
          :fileName="analysis.fileName"
          :showDetails="false"
          :showActions="true"
          @cancel="cancelAnalysis(trackerId)"
          @retry="retryAnalysis(trackerId)"
        />
      </div>
    </div>

    <div v-if="uploadedFiles.length > 0" class="space-y-4">
      <h4 class="font-medium text-gray-900">Przesłane pliki</h4>
      
      <div class="space-y-2">
        <div
          v-for="file in uploadedFiles"
          :key="file.id"
          class="flex items-center justify-between p-3 bg-success-50 rounded-md"
        >
          <div class="flex items-center space-x-3">
            <CheckCircleIcon class="h-5 w-5 text-success-600" />
            <div>
              <p class="text-sm font-medium text-gray-900">{{ file.fileName }}</p>
              <p class="text-xs text-gray-500">
                Przesłano: {{ new Date(file.uploadDate).toLocaleString('pl-PL') }}
              </p>
            </div>
          </div>
          
          <router-link
            v-if="file.analyzed"
            :to="`/results/${file.id}`"
            class="text-primary-600 hover:text-primary-700 text-sm font-medium"
          >
            Zobacz wyniki
          </router-link>
          <span v-else class="text-amber-600 text-sm font-medium">
            Analizowanie...
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  CloudArrowUpIcon,
  DocumentTextIcon,
  XMarkIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline'
import { useInvoicesStore } from '@/stores/invoices'
import { useApiKeyStore } from '@/stores/apiKey'
import { useInvoiceAnalysis } from '@/composables/useInvoiceAnalysis'
import ProgressIndicator from '@/components/common/ProgressIndicator.vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const invoicesStore = useInvoicesStore()
const apiKeyStore = useApiKeyStore()
const { analyzeFile, getProgressDetails, getCurrentStage } = useInvoiceAnalysis()

const fileInput = ref<HTMLInputElement>()
const selectedFiles = ref<File[]>([])
const isDragOver = ref(false)
const isUploading = ref(false)
const activeAnalyses = ref<Record<string, { fileName: string, invoiceId: string }>>({})
const error = ref('')

const uploadedFiles = computed(() => invoicesStore.invoices.slice(-5))

const ALLOWED_TYPES = ['application/pdf', 'image/jpeg', 'image/jpg', 'image/png', 'image/webp']
const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB

function handleDragOver(e: DragEvent) {
  e.preventDefault()
  isDragOver.value = true
}

function handleDragEnter(e: DragEvent) {
  e.preventDefault()
  isDragOver.value = true
}

function handleDragLeave(e: DragEvent) {
  e.preventDefault()
  isDragOver.value = false
}

function handleDrop(e: DragEvent) {
  e.preventDefault()
  isDragOver.value = false
  
  const files = Array.from(e.dataTransfer?.files || [])
  addFiles(files)
}

function triggerFileInput() {
  fileInput.value?.click()
}

function handleFileSelect(e: Event) {
  const target = e.target as HTMLInputElement
  const files = Array.from(target.files || [])
  addFiles(files)
}

function addFiles(files: File[]) {
  error.value = ''
  
  const validFiles = files.filter(file => {
    if (!ALLOWED_TYPES.includes(file.type)) {
      error.value = `Nieobsługiwany format pliku: ${file.name}`
      return false
    }
    
    if (file.size > MAX_FILE_SIZE) {
      error.value = `Plik ${file.name} jest zbyt duży (max 10MB)`
      return false
    }
    
    return true
  })
  
  selectedFiles.value = [...selectedFiles.value, ...validFiles]
}

function removeFile(index: number) {
  selectedFiles.value.splice(index, 1)
}

function clearFiles() {
  selectedFiles.value = []
  error.value = ''
}

async function uploadFiles() {
  if (!apiKeyStore.isConfigured) {
    error.value = 'Skonfiguruj najpierw klucz API w ustawieniach'
    return
  }
  
  if (selectedFiles.value.length === 0) return
  
  isUploading.value = true
  error.value = ''
  
  try {
    const filesToProcess = [...selectedFiles.value]
    const isSingleFile = filesToProcess.length === 1
    let singleFileInvoiceId = ''
    
    // Rozpocznij analizę dla każdego pliku
    for (const file of filesToProcess) {
      // Dodaj fakturę do store'a
      const invoiceId = invoicesStore.addInvoice(file)
      const trackerId = invoiceId
      
      // Zapisz ID pierwszego (i jedynego) pliku dla przekierowania
      if (isSingleFile) {
        singleFileInvoiceId = invoiceId
      }
      
      // Dodaj do aktywnych analiz
      activeAnalyses.value[trackerId] = {
        fileName: file.name,
        invoiceId
      }
      
      // Rozpocznij analizę asynchronicznie
      analyzeFile(file, invoiceId)
        .then(() => {
          // Usuń z aktywnych analiz po zakończeniu
          delete activeAnalyses.value[trackerId]
          
          // Jeśli to był pojedynczy plik, przekieruj do wyników
          if (isSingleFile && singleFileInvoiceId === invoiceId) {
            router.push(`/results/${invoiceId}`)
          }
        })
        .catch((analysisError) => {
          console.error(`Błąd analizy dla ${file.name}:`, analysisError)
          // Usuń z aktywnych analiz nawet przy błędzie
          delete activeAnalyses.value[trackerId]
          
          // Jeśli to był pojedynczy plik z błędem, i tak przekieruj (wyniki pokażą błąd)
          if (isSingleFile && singleFileInvoiceId === invoiceId) {
            router.push(`/results/${invoiceId}`)
          }
        })
    }
    
    // Wyczyść wybrane pliki natychmiast po rozpoczęciu przetwarzania
    selectedFiles.value = []
    
    // Jeśli jest wiele plików - przekieruj na historię po chwili
    if (!isSingleFile) {
      setTimeout(() => {
        router.push('/history')
      }, 1000)
    }
    // Pojedynczy plik: pozostań na upload page i czekaj na zakończenie analizy
    
  } catch (err) {
    error.value = 'Błąd podczas przesyłania plików. Spróbuj ponownie.'
  } finally {
    isUploading.value = false
  }
}

function cancelAnalysis(trackerId: string) {
  // Usuń z aktywnych analiz
  delete activeAnalyses.value[trackerId]
  
  // TODO: Implement actual cancellation of API request
  // W prawdziwej implementacji trzeba by anulować zapytanie do API
}

function retryAnalysis(trackerId: string) {
  const analysis = activeAnalyses.value[trackerId]
  if (!analysis) return
  
  // Znajdź oryginalny plik (w prawdziwej implementacji trzeba by go przechować)
  // Na razie po prostu usuń z aktywnych
  delete activeAnalyses.value[trackerId]
  error.value = 'Retry nie jest jeszcze zaimplementowane. Prześlij plik ponownie.'
}

function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

onMounted(() => {
  // Sprawdź czy użytkownik ma skonfigurowany klucz API
  if (!apiKeyStore.isConfigured) {
    error.value = 'Aby przesyłać pliki, najpierw skonfiguruj klucz API w ustawieniach.'
  }
})
</script>