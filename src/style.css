@import "tailwindcss";

@theme {
  /* Modern neutral palette inspired by ChatGPT/Linear */
  --color-neutral-50: #fafafa;
  --color-neutral-100: #f5f5f5;
  --color-neutral-200: #e5e5e5;
  --color-neutral-300: #d4d4d4;
  --color-neutral-400: #a3a3a3;
  --color-neutral-500: #737373;
  --color-neutral-600: #525252;
  --color-neutral-700: #404040;
  --color-neutral-800: #262626;
  --color-neutral-900: #171717;
  --color-neutral-950: #0a0a0a;
  
  /* Premium primary colors - subtle purple/indigo */
  --color-primary-50: #f8fafc;
  --color-primary-100: #f1f5f9;
  --color-primary-200: #e2e8f0;
  --color-primary-300: #cbd5e1;
  --color-primary-400: #94a3b8;
  --color-primary-500: #64748b;
  --color-primary-600: #475569;
  --color-primary-700: #334155;
  --color-primary-800: #1e293b;
  --color-primary-900: #0f172a;
  
  /* Accent color - modern purple */
  --color-accent-50: #faf5ff;
  --color-accent-100: #f3e8ff;
  --color-accent-200: #e9d5ff;
  --color-accent-300: #d8b4fe;
  --color-accent-400: #c084fc;
  --color-accent-500: #a855f7;
  --color-accent-600: #9333ea;
  --color-accent-700: #7c3aed;
  --color-accent-800: #6b21a8;
  --color-accent-900: #581c87;
  
  /* Success - modern green */
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;
  
  /* Error - modern red */
  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-300: #fca5a5;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-800: #991b1b;
  --color-error-900: #7f1d1d;
  
  /* Warning - modern amber */
  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;
  
  /* Typography - premium font stack */
  --font-family-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'Source Code Pro', Consolas, 'Liberation Mono', Menlo, monospace;
  
  /* Spacing scale */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;
  --spacing-3xl: 4rem;
  
  /* Border radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  /* Shadows - subtle and modern */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

html {
  font-family: var(--font-family-sans);
  line-height: 1.6;
  font-weight: 400;
  color-scheme: light;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  color: var(--color-neutral-800);
  font-size: 14px;
}

#app {
  width: 100%;
  margin: 0;
  padding: 0;
  text-align: left;
  min-height: 100vh;
}

/* Modern button styles */
.btn-primary {
  @apply inline-flex items-center justify-center;
  padding: 10px 20px;
  border-radius: var(--radius-lg);
  background: linear-gradient(135deg, var(--color-accent-600) 0%, var(--color-accent-700) 100%);
  color: white;
  font-size: 14px;
  font-weight: 500;
  font-family: var(--font-family-sans);
  border: none;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
  min-height: 44px;
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-accent-700) 0%, var(--color-accent-800) 100%);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-primary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-sm);
}

.btn-secondary {
  @apply inline-flex items-center justify-center;
  padding: 10px 20px;
  border-radius: var(--radius-lg);
  background: white;
  color: var(--color-neutral-700);
  font-size: 14px;
  font-weight: 500;
  font-family: var(--font-family-sans);
  border: 1px solid var(--color-neutral-200);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
  min-height: 44px;
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-neutral-50);
  border-color: var(--color-neutral-300);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.btn-secondary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Modern card styles */
.card {
  background: white;
  border-radius: var(--radius-xl);
  border: 1px solid var(--color-neutral-200);
  box-shadow: var(--shadow-md);
  padding: 24px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  border-color: var(--color-neutral-300);
}

/* Modern input styles */
.input-field {
  @apply block w-full;
  padding: 12px 16px;
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-neutral-300);
  background: white;
  color: var(--color-neutral-900);
  font-size: 14px;
  font-family: var(--font-family-sans);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 44px;
}

.input-field:focus {
  outline: none;
  border-color: var(--color-accent-500);
  box-shadow: 0 0 0 3px var(--color-accent-100);
}

.input-field::placeholder {
  color: var(--color-neutral-400);
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
  from { opacity: 0; transform: translateX(-10px); }
  to { opacity: 1; transform: translateX(0); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.animate-fade-in {
  animation: fadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-slide-in {
  animation: slideIn 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-pulse-slow {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.2, 1) infinite;
}

/* Navigation styles */
.nav-item {
  @apply flex items-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200;
  color: var(--color-neutral-600);
}

.nav-item:hover {
  background: var(--color-neutral-100);
  color: var(--color-neutral-800);
}

.nav-item--active {
  background: var(--color-accent-50);
  color: var(--color-accent-700);
}

.nav-item--active:hover {
  background: var(--color-accent-100);
  color: var(--color-accent-800);
}

/* Glassmorphism utilities */
.backdrop-blur-md {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

/* Professional spacing utilities */
.section-spacing {
  margin-bottom: 48px;
}

.section-spacing:last-child {
  margin-bottom: 0;
}

/* Modern text styles */
.text-heading-1 {
  font-size: 32px;
  font-weight: 700;
  line-height: 1.2;
  color: var(--color-neutral-900);
}

.text-heading-2 {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.3;
  color: var(--color-neutral-900);
}

.text-heading-3 {
  font-size: 20px;
  font-weight: 600;
  line-height: 1.4;
  color: var(--color-neutral-900);
}

.text-body {
  font-size: 14px;
  font-weight: 400;
  line-height: 1.6;
  color: var(--color-neutral-700);
}

.text-caption {
  font-size: 12px;
  font-weight: 400;
  line-height: 1.5;
  color: var(--color-neutral-500);
}