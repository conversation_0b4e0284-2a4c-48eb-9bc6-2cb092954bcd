<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Historia faktur</h1>
        <p class="mt-2 text-gray-600">
          Prz<PERSON>ląd {{ totalInvoices }} prz<PERSON>łanych faktur
        </p>
      </div>
      
      <div class="flex space-x-3">
        <router-link to="/upload" class="btn-primary">
          Prześlij nowe faktury
        </router-link>
        
        <button
          v-if="totalInvoices > 0"
          @click="showClearConfirm = true"
          class="btn-secondary text-error-600 hover:text-error-700"
        >
          W<PERSON><PERSON><PERSON><PERSON><PERSON> historię
        </button>
      </div>
    </div>

    <!-- Filters -->
    <div class="card">
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <label for="search" class="block text-sm font-medium text-gray-700 mb-1">
            Wyszukaj
          </label>
          <div class="relative">
            <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              id="search"
              v-model="searchQuery"
              type="text"
              placeholder="Nazwa pliku, numer faktury..."
              class="input-field pl-10"
            />
          </div>
        </div>
        
        <div>
          <label for="filter" class="block text-sm font-medium text-gray-700 mb-1">
            Filtruj
          </label>
          <select v-model="filterStatus" class="input-field">
            <option value="all">Wszystkie</option>
            <option value="analyzed">Przeanalizowane</option>
            <option value="pending">Oczekujące</option>
          </select>
        </div>
        
        <div>
          <label for="sort" class="block text-sm font-medium text-gray-700 mb-1">
            Sortuj
          </label>
          <select v-model="sortBy" class="input-field">
            <option value="uploadDate-desc">Najnowsze</option>
            <option value="uploadDate-asc">Najstarsze</option>
            <option value="fileName-asc">Nazwa A-Z</option>
            <option value="fileName-desc">Nazwa Z-A</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Statistics -->
    <div v-if="totalInvoices > 0" class="grid grid-cols-1 md:grid-cols-3 gap-6">
      <div class="card text-center">
        <DocumentTextIcon class="mx-auto h-8 w-8 text-primary-600 mb-2" />
        <div class="text-2xl font-bold text-gray-900">{{ totalInvoices }}</div>
        <div class="text-sm text-gray-500">Łączna liczba faktur</div>
      </div>
      
      <div class="card text-center">
        <CheckCircleIcon class="mx-auto h-8 w-8 text-success-600 mb-2" />
        <div class="text-2xl font-bold text-gray-900">{{ analyzedInvoices.length }}</div>
        <div class="text-sm text-gray-500">Przeanalizowane</div>
      </div>
      
      <div class="card text-center">
        <ClockIcon class="mx-auto h-8 w-8 text-amber-600 mb-2" />
        <div class="text-2xl font-bold text-gray-900">{{ pendingInvoices.length }}</div>
        <div class="text-sm text-gray-500">Oczekujące</div>
      </div>
    </div>

    <!-- Empty state -->
    <div v-if="totalInvoices === 0" class="card text-center py-12">
      <DocumentTextIcon class="mx-auto h-12 w-12 text-gray-400" />
      <h3 class="mt-4 text-lg font-medium text-gray-900">Brak faktur</h3>
      <p class="mt-2 text-gray-500">
        Nie przesłałeś jeszcze żadnych faktur do analizy.
      </p>
      <div class="mt-4">
        <router-link to="/upload" class="btn-primary">
          Prześlij pierwszą fakturę
        </router-link>
      </div>
    </div>

    <!-- Invoice list -->
    <div v-else-if="filteredInvoices.length === 0" class="card text-center py-8">
      <MagnifyingGlassIcon class="mx-auto h-8 w-8 text-gray-400" />
      <h3 class="mt-2 text-lg font-medium text-gray-900">Brak wyników</h3>
      <p class="mt-1 text-gray-500">
        Nie znaleziono faktur pasujących do kryteriów wyszukiwania.
      </p>
    </div>

    <div v-else class="space-y-4">
      <div
        v-for="invoice in paginatedInvoices"
        :key="invoice.id"
        class="card hover:shadow-md transition-shadow"
      >
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="flex-shrink-0">
              <div
                :class="[
                  'w-10 h-10 rounded-lg flex items-center justify-center',
                  invoice.analyzed 
                    ? 'bg-success-100 text-success-600' 
                    : 'bg-amber-100 text-amber-600'
                ]"
              >
                <CheckCircleIcon v-if="invoice.analyzed" class="h-5 w-5" />
                <ClockIcon v-else class="h-5 w-5" />
              </div>
            </div>
            
            <div class="min-w-0 flex-1">
              <p class="text-sm font-medium text-gray-900 truncate">
                {{ invoice.fileName }}
              </p>
              <div class="flex items-center space-x-4 mt-1 text-xs text-gray-500">
                <span>{{ formatDate(invoice.uploadDate) }}</span>
                
                <span v-if="invoice.analyzed && invoice.analysisData" class="flex items-center space-x-2">
                  <span>{{ invoice.analysisData.invoiceDetails?.number || 'Brak numeru' }}</span>
                  <span>•</span>
                  <span>{{ formatCurrency(invoice.analysisData.financialData?.grossAmount) }}</span>
                </span>
                
                <span v-else class="text-amber-600">
                  Oczekuje na analizę
                </span>
              </div>
            </div>
          </div>
          
          <div class="flex items-center space-x-3">
            <router-link
              v-if="invoice.analyzed"
              :to="`/results/${invoice.id}`"
              class="text-primary-600 hover:text-primary-700 text-sm font-medium"
            >
              Zobacz wyniki
            </router-link>
            
            <button
              v-else
              @click="startAnalysis(invoice)"
              :disabled="isAnalyzing"
              class="text-primary-600 hover:text-primary-700 text-sm font-medium disabled:opacity-50"
            >
              Analizuj teraz
            </button>
            
            <Menu as="div" class="relative">
              <MenuButton class="p-1 rounded-md text-gray-400 hover:text-gray-600">
                <EllipsisVerticalIcon class="h-4 w-4" />
              </MenuButton>
              
              <MenuItems class="absolute right-0 mt-1 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-10">
                <MenuItem v-slot="{ active }">
                  <button
                    :class="[
                      active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                      'block w-full text-left px-4 py-2 text-sm'
                    ]"
                    @click="downloadInvoiceData(invoice)"
                  >
                    Eksportuj JSON
                  </button>
                </MenuItem>
                
                <MenuItem v-slot="{ active }">
                  <button
                    :class="[
                      active ? 'bg-gray-100 text-gray-900' : 'text-gray-700',
                      'block w-full text-left px-4 py-2 text-sm'
                    ]"
                    @click="confirmDelete(invoice)"
                  >
                    Usuń fakturę
                  </button>
                </MenuItem>
              </MenuItems>
            </Menu>
          </div>
        </div>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="flex items-center justify-between border-t border-gray-200 bg-white px-4 py-3 sm:px-6">
        <div class="flex flex-1 justify-between sm:hidden">
          <button
            @click="currentPage > 1 && (currentPage--)"
            :disabled="currentPage === 1"
            class="relative inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
          >
            Poprzednia
          </button>
          <button
            @click="currentPage < totalPages && (currentPage++)"
            :disabled="currentPage === totalPages"
            class="relative ml-3 inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 disabled:opacity-50"
          >
            Następna
          </button>
        </div>
        
        <div class="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
          <div>
            <p class="text-sm text-gray-700">
              Pokazujesz
              <span class="font-medium">{{ (currentPage - 1) * itemsPerPage + 1 }}</span>
              do
              <span class="font-medium">{{ Math.min(currentPage * itemsPerPage, filteredInvoices.length) }}</span>
              z
              <span class="font-medium">{{ filteredInvoices.length }}</span>
              wyników
            </p>
          </div>
          
          <nav class="isolate inline-flex -space-x-px rounded-md shadow-sm">
            <button
              @click="currentPage > 1 && (currentPage--)"
              :disabled="currentPage === 1"
              class="relative inline-flex items-center rounded-l-md border border-gray-300 bg-white px-2 py-2 text-gray-500 hover:bg-gray-50 disabled:opacity-50"
            >
              <ChevronLeftIcon class="h-5 w-5" />
            </button>
            
            <template v-for="page in visiblePages" :key="page">
              <button
                @click="currentPage = page"
                :class="[
                  'relative inline-flex items-center border px-4 py-2 text-sm font-medium',
                  page === currentPage
                    ? 'z-10 bg-primary-50 border-primary-500 text-primary-600'
                    : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                ]"
              >
                {{ page }}
              </button>
            </template>
            
            <button
              @click="currentPage < totalPages && (currentPage++)"
              :disabled="currentPage === totalPages"
              class="relative inline-flex items-center rounded-r-md border border-gray-300 bg-white px-2 py-2 text-gray-500 hover:bg-gray-50 disabled:opacity-50"
            >
              <ChevronRightIcon class="h-5 w-5" />
            </button>
          </nav>
        </div>
      </div>
    </div>

    <!-- Confirmation dialogs -->
    <div
      v-if="showDeleteConfirm"
      class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50"
      @click="showDeleteConfirm = false"
    >
      <div class="card max-w-md" @click.stop>
        <h3 class="text-lg font-medium text-gray-900 mb-4">Usuń fakturę</h3>
        <p class="text-sm text-gray-600 mb-4">
          Czy na pewno chcesz usunąć fakturę "{{ invoiceToDelete?.fileName }}"? Ta operacja jest nieodwracalna.
        </p>
        <div class="flex space-x-3">
          <button
            @click="deleteInvoice"
            class="btn-primary bg-error-600 hover:bg-error-700"
          >
            Usuń fakturę
          </button>
          <button
            @click="showDeleteConfirm = false"
            class="btn-secondary"
          >
            Anuluj
          </button>
        </div>
      </div>
    </div>

    <div
      v-if="showClearConfirm"
      class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50"
      @click="showClearConfirm = false"
    >
      <div class="card max-w-md" @click.stop>
        <h3 class="text-lg font-medium text-gray-900 mb-4">Wyczyść całą historię</h3>
        <p class="text-sm text-gray-600 mb-4">
          Czy na pewno chcesz usunąć wszystkie faktury? Ta operacja jest nieodwracalna.
        </p>
        <div class="flex space-x-3">
          <button
            @click="clearAllInvoices"
            class="btn-primary bg-error-600 hover:bg-error-700"
          >
            Usuń wszystkie
          </button>
          <button
            @click="showClearConfirm = false"
            class="btn-secondary"
          >
            Anuluj
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  DocumentTextIcon,
  CheckCircleIcon,
  ClockIcon,
  MagnifyingGlassIcon,
  EllipsisVerticalIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/vue/24/outline'
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import { useInvoicesStore } from '@/stores/invoices'
import { useInvoiceAnalysis } from '@/composables/useInvoiceAnalysis'
import type { InvoiceData } from '@/types'

const invoicesStore = useInvoicesStore()
const { analyzeFile, isAnalyzing } = useInvoiceAnalysis()

// Search and filters
const searchQuery = ref('')
const filterStatus = ref<'all' | 'analyzed' | 'pending'>('all')
const sortBy = ref<'uploadDate-desc' | 'uploadDate-asc' | 'fileName-asc' | 'fileName-desc'>('uploadDate-desc')

// Pagination
const currentPage = ref(1)
const itemsPerPage = ref(10)

// Dialogs
const showDeleteConfirm = ref(false)
const showClearConfirm = ref(false)
const invoiceToDelete = ref<InvoiceData | null>(null)

// Computed
const totalInvoices = computed(() => invoicesStore.invoices.length)
const analyzedInvoices = computed(() => invoicesStore.analyzedInvoices)
const pendingInvoices = computed(() => invoicesStore.pendingInvoices)

const filteredInvoices = computed(() => {
  let filtered = [...invoicesStore.invoices]

  // Search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(invoice =>
      invoice.fileName.toLowerCase().includes(query) ||
      invoice.analysisData?.invoiceDetails?.number?.toLowerCase().includes(query)
    )
  }

  // Status filter
  if (filterStatus.value === 'analyzed') {
    filtered = filtered.filter(invoice => invoice.analyzed)
  } else if (filterStatus.value === 'pending') {
    filtered = filtered.filter(invoice => !invoice.analyzed)
  }

  // Sort
  filtered.sort((a, b) => {
    switch (sortBy.value) {
      case 'uploadDate-desc':
        return new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime()
      case 'uploadDate-asc':
        return new Date(a.uploadDate).getTime() - new Date(b.uploadDate).getTime()
      case 'fileName-asc':
        return a.fileName.localeCompare(b.fileName, 'pl')
      case 'fileName-desc':
        return b.fileName.localeCompare(a.fileName, 'pl')
      default:
        return 0
    }
  })

  return filtered
})

const totalPages = computed(() => Math.ceil(filteredInvoices.value.length / itemsPerPage.value))

const paginatedInvoices = computed(() => {
  const start = (currentPage.value - 1) * itemsPerPage.value
  const end = start + itemsPerPage.value
  return filteredInvoices.value.slice(start, end)
})

const visiblePages = computed(() => {
  const pages = []
  const total = totalPages.value
  const current = currentPage.value

  if (total <= 7) {
    for (let i = 1; i <= total; i++) {
      pages.push(i)
    }
  } else {
    if (current <= 4) {
      for (let i = 1; i <= 5; i++) pages.push(i)
      pages.push('...', total)
    } else if (current >= total - 3) {
      pages.push(1, '...')
      for (let i = total - 4; i <= total; i++) pages.push(i)
    } else {
      pages.push(1, '...')
      for (let i = current - 1; i <= current + 1; i++) pages.push(i)
      pages.push('...', total)
    }
  }

  return pages.filter(p => p !== '...').map(p => Number(p))
})

// Methods
const formatDate = (date: Date): string => {
  return date.toLocaleDateString('pl-PL', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const formatCurrency = (amount: number | null | undefined): string => {
  if (!amount) return 'Brak danych'
  return new Intl.NumberFormat('pl-PL', {
    style: 'currency',
    currency: 'PLN'
  }).format(amount)
}

async function startAnalysis(invoice: InvoiceData) {
  // W rzeczywistej implementacji trzeba by mieć dostęp do oryginalnego pliku
  // Na razie symulujemy analizę
  console.log('Starting analysis for:', invoice.fileName)
}

function confirmDelete(invoice: InvoiceData) {
  invoiceToDelete.value = invoice
  showDeleteConfirm.value = true
}

function deleteInvoice() {
  if (invoiceToDelete.value) {
    invoicesStore.removeInvoice(invoiceToDelete.value.id)
    showDeleteConfirm.value = false
    invoiceToDelete.value = null
  }
}

function clearAllInvoices() {
  invoicesStore.clearAll()
  showClearConfirm.value = false
  currentPage.value = 1
}

function downloadInvoiceData(invoice: InvoiceData) {
  if (!invoice.analysisData) return

  const dataStr = JSON.stringify(invoice, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  
  const link = document.createElement('a')
  link.href = URL.createObjectURL(dataBlob)
  link.download = `invoice-${invoice.id}-${Date.now()}.json`
  link.click()
  
  URL.revokeObjectURL(link.href)
}

onMounted(() => {
  invoicesStore.loadFromStorage()
})
</script>