<template>
  <div class="max-w-2xl mx-auto section-spacing animate-fade-in">
    <div class="text-center mb-8">
      <h1 class="text-heading-1 mb-4">Ustawienia</h1>
      <p class="text-body max-w-lg mx-auto">
        Skonfiguruj klucz API Google Gemini i inne preferencje aplikacji.
      </p>
    </div>

    <div class="card">
      <div class="flex items-center mb-6">
        <div class="flex items-center justify-center w-10 h-10 bg-accent-100 rounded-xl mr-4">
          <svg class="w-5 h-5 text-accent-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
          </svg>
        </div>
        <h2 class="text-heading-2">Klucz API Google Gemini</h2>
      </div>
      
      <div v-if="apiKeyStore.isConfigured" class="space-y-4">
        <div class="flex items-center justify-between p-5 bg-success-50 rounded-xl border border-success-200">
          <div class="flex items-center">
            <div class="flex items-center justify-center w-8 h-8 bg-success-100 rounded-full mr-3">
              <CheckCircleIcon class="h-4 w-4 text-success-600" />
            </div>
            <div>
              <p class="text-sm font-semibold text-success-800">
                Klucz API skonfigurowany
              </p>
              <p class="text-caption text-success-600 font-mono">
                {{ apiKeyStore.maskedKey }}
              </p>
            </div>
          </div>
          <button
            @click="showChangeKey = true"
            class="btn-secondary"
          >
            Zmień klucz
          </button>
        </div>
        
        <div v-if="apiKeyStore.lastValidated" class="text-caption">
          Ostatnia walidacja: {{ new Date(apiKeyStore.lastValidated).toLocaleString('pl-PL') }}
        </div>
      </div>

      <div v-if="!apiKeyStore.isConfigured || showChangeKey" class="space-y-6">
        <div>
          <label for="apiKey" class="block text-sm font-semibold mb-2" style="color: var(--color-neutral-700)">
            Klucz API Google Gemini
          </label>
          <input
            id="apiKey"
            v-model="apiKeyInput"
            type="password"
            class="input-field"
            placeholder="Wprowadź swój klucz API..."
          />
          <p class="mt-2 text-caption">
            Klucz API jest przechowywany tylko lokalnie w Twojej przeglądarce.
          </p>
        </div>

        <div class="flex space-x-3">
          <button
            @click="saveApiKey"
            :disabled="!apiKeyInput.trim() || isValidating"
            class="btn-primary"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
            </svg>
            <span v-if="isValidating">Walidacja...</span>
            <span v-else>Zapisz klucz</span>
          </button>
          
          <button
            v-if="showChangeKey"
            @click="cancelChange"
            class="btn-secondary"
          >
            Anuluj
          </button>
          
          <button
            v-if="apiKeyStore.isConfigured"
            @click="clearApiKey"
            class="btn-secondary"
            style="color: var(--color-error-600)"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Usuń klucz
          </button>
        </div>
      </div>

      <div v-if="error" class="mt-6 p-4 bg-error-50 rounded-xl border border-error-200">
        <div class="flex">
          <div class="flex items-center justify-center w-8 h-8 bg-error-100 rounded-full mr-3 flex-shrink-0">
            <ExclamationTriangleIcon class="h-4 w-4 text-error-600" />
          </div>
          <div class="text-sm font-medium text-error-800">
            {{ error }}
          </div>
        </div>
      </div>

      <div class="mt-8 pt-6 border-t" style="border-color: var(--color-neutral-200)">
        <h3 class="text-heading-3 mb-4">Jak uzyskać klucz API?</h3>
        <div class="space-y-3">
          <div class="flex items-start">
            <div class="flex items-center justify-center w-6 h-6 bg-accent-100 text-accent-700 rounded-full text-xs font-semibold mr-3 flex-shrink-0 mt-0.5">1</div>
            <p class="text-body">Przejdź na stronę <a href="https://makersuite.google.com/app/apikey" target="_blank" class="text-accent-600 hover:text-accent-700 font-medium underline">Google AI Studio</a></p>
          </div>
          <div class="flex items-start">
            <div class="flex items-center justify-center w-6 h-6 bg-accent-100 text-accent-700 rounded-full text-xs font-semibold mr-3 flex-shrink-0 mt-0.5">2</div>
            <p class="text-body">Zaloguj się na swoje konto Google</p>
          </div>
          <div class="flex items-start">
            <div class="flex items-center justify-center w-6 h-6 bg-accent-100 text-accent-700 rounded-full text-xs font-semibold mr-3 flex-shrink-0 mt-0.5">3</div>
            <p class="text-body">Kliknij "Create API Key"</p>
          </div>
          <div class="flex items-start">
            <div class="flex items-center justify-center w-6 h-6 bg-accent-100 text-accent-700 rounded-full text-xs font-semibold mr-3 flex-shrink-0 mt-0.5">4</div>
            <p class="text-body">Skopiuj wygenerowany klucz i wklej go powyżej</p>
          </div>
        </div>
      </div>
    </div>

    <div class="card">
      <div class="flex items-center mb-4">
        <div class="flex items-center justify-center w-10 h-10 bg-warning-100 rounded-xl mr-4">
          <svg class="w-5 h-5 text-warning-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 7v10c0 2.21 3.31 4 7 4s7-1.79 7-4V7c0-2.21-3.31-4-7-4S4 4.79 4 7z" />
          </svg>
        </div>
        <h2 class="text-heading-2">Dane lokalne</h2>
      </div>
      <p class="text-body mb-6">
        Wszystkie dane (klucz API, historia faktur) są przechowywane lokalnie w Twojej przeglądarce.
      </p>
      
      <button
        @click="confirmClearAll"
        class="btn-secondary"
        style="color: var(--color-error-600)"
      >
        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
        </svg>
        Wyczyść wszystkie dane
      </button>
    </div>

    <div
      v-if="showClearConfirm"
      class="fixed inset-0 bg-neutral-500 bg-opacity-75 flex items-center justify-center z-50"
      @click="showClearConfirm = false"
    >
      <div class="card max-w-md" @click.stop>
        <h3 class="text-heading-3 mb-4">Potwierdź usunięcie</h3>
        <p class="text-body mb-4">
          Czy na pewno chcesz usunąć wszystkie dane? Ta operacja jest nieodwracalna.
        </p>
        <div class="flex space-x-3">
          <button
            @click="clearAllData"
            class="btn-primary"
            style="background: linear-gradient(135deg, var(--color-error-600) 0%, var(--color-error-700) 100%)"
          >
            Usuń wszystkie dane
          </button>
          <button
            @click="showClearConfirm = false"
            class="btn-secondary"
          >
            Anuluj
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline'
import { useApiKeyStore } from '@/stores/apiKey'
import { useInvoicesStore } from '@/stores/invoices'
import { useRouter } from 'vue-router'

const router = useRouter()
const apiKeyStore = useApiKeyStore()
const invoicesStore = useInvoicesStore()

const apiKeyInput = ref('')
const isValidating = ref(false)
const error = ref('')
const showChangeKey = ref(false)
const showClearConfirm = ref(false)

async function saveApiKey() {
  if (!apiKeyInput.value.trim()) return
  
  isValidating.value = true
  error.value = ''
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    apiKeyStore.setApiKey(apiKeyInput.value.trim())
    apiKeyStore.validateApiKey()
    
    apiKeyInput.value = ''
    showChangeKey.value = false
    
  } catch (err) {
    error.value = 'Nieprawidłowy klucz API. Sprawdź czy klucz jest poprawny.'
  } finally {
    isValidating.value = false
  }
}

function clearApiKey() {
  apiKeyStore.clearApiKey()
  apiKeyInput.value = ''
  showChangeKey.value = false
  error.value = ''
}

function cancelChange() {
  showChangeKey.value = false
  apiKeyInput.value = ''
  error.value = ''
}

function confirmClearAll() {
  showClearConfirm.value = true
}

function clearAllData() {
  apiKeyStore.clearApiKey()
  invoicesStore.clearAll()
  showClearConfirm.value = false
  router.push('/')
}
</script>