<template>
  <div class="space-y-12">
    <!-- Hero section -->
    <div class="text-center max-w-4xl mx-auto">
      <div class="animate-fade-in">
        <div class="inline-flex items-center px-4 py-2 bg-accent-50 text-accent-700 rounded-full text-sm font-medium mb-6">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
          </svg>
          AI-powered invoice analysis
        </div>
        <h1 class="text-heading-1 mb-6">
          Analizuj faktury energetyczne<br>
          <span class="bg-gradient-to-r from-accent-600 to-accent-700 bg-clip-text text-transparent">
            w mgnieniu oka
          </span>
        </h1>
        <p class="text-xl text-body max-w-4xl mx-auto">
          Automatyczne wyciąganie danych z faktur za energię elektryczną przy użyciu sztucznej inteligencji Google Gemini
        </p>
      </div>
    </div>

    <!-- API Key setup notification -->
    <div v-if="!apiKeyStore.isConfigured" class="section-spacing">
      <div class="card max-w-3xl mx-auto text-center animate-fade-in">
        <div class="flex items-center justify-center w-16 h-16 bg-warning-100 rounded-full mx-auto mb-4">
          <ExclamationTriangleIcon class="w-8 h-8 text-warning-600" />
        </div>
        <h3 class="text-heading-3 mb-2">Skonfiguruj klucz API</h3>
        <p class="text-body mb-6">
          Aby korzystać z analizy faktur, musisz najpierw skonfigurować klucz API Google Gemini.
        </p>
        <router-link to="/settings" class="btn-primary">
          <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          </svg>
          Skonfiguruj klucz API
        </router-link>
      </div>
    </div>

    <!-- Main features grid -->
    <div v-else class="section-spacing">
      <div class="grid grid-cols-1 gap-6 lg:grid-cols-3 animate-fade-in">
        <div class="card group hover:scale-[1.02] transition-all duration-300">
          <div class="flex items-center justify-center w-12 h-12 bg-accent-100 rounded-xl mb-4 group-hover:bg-accent-200 transition-colors">
            <DocumentTextIcon class="w-6 h-6 text-accent-600" />
          </div>
          <h3 class="text-heading-3 mb-2">Prześlij fakturę</h3>
          <p class="text-body mb-6">
            Dodaj nową fakturę do analizy AI w kilka sekund
          </p>
          <router-link to="/upload" class="btn-primary w-full group">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            Upload pliku
          </router-link>
        </div>

        <div class="card group hover:scale-[1.02] transition-all duration-300">
          <div class="flex items-center justify-center w-12 h-12 bg-success-100 rounded-xl mb-4 group-hover:bg-success-200 transition-colors">
            <ClockIcon class="w-6 h-6 text-success-600" />
          </div>
          <h3 class="text-heading-3 mb-2">Historia</h3>
          <p class="text-body mb-6">
            {{ analyzedInvoices.length }} przeanalizowanych faktur
          </p>
          <router-link to="/history" class="btn-secondary w-full">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Zobacz historię
          </router-link>
        </div>

        <div class="card group hover:scale-[1.02] transition-all duration-300 relative overflow-hidden">
          <div class="flex items-center justify-center w-12 h-12 bg-warning-100 rounded-xl mb-4 group-hover:bg-warning-200 transition-colors">
            <ChartBarIcon class="w-6 h-6 text-warning-600" />
          </div>
          <h3 class="text-heading-3 mb-2">Statystyki</h3>
          <p class="text-body mb-6">
            Analiza zużycia energii i trendy
          </p>
          <button class="btn-secondary w-full opacity-60" disabled>
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            Wkrótce dostępne
          </button>
          <div class="absolute top-4 right-4">
            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-warning-100 text-warning-800">
              Soon
            </span>
          </div>
        </div>
      </div>
    </div>

    <div v-if="apiKeyStore.isConfigured && analyzedInvoices.length > 0" class="card">
      <h2 class="text-heading-3 mb-6">Ostatnie analizy</h2>
      <div class="space-y-3">
        <div
          v-for="invoice in analyzedInvoices.slice(0, 3)"
          :key="invoice.id"
          class="flex items-center justify-between p-4 bg-neutral-50 rounded-xl border border-neutral-200 hover:border-neutral-300 transition-colors"
        >
          <div>
            <p class="font-semibold text-neutral-900">{{ invoice.fileName }}</p>
            <p class="text-caption text-neutral-500">
              {{ new Date(invoice.uploadDate).toLocaleDateString('pl-PL') }}
            </p>
          </div>
          <router-link
            :to="`/results/${invoice.id}`"
            class="btn-secondary text-sm"
          >
            Zobacz wyniki
          </router-link>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  DocumentTextIcon,
  ClockIcon,
  ChartBarIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline'
import { useApiKeyStore } from '@/stores/apiKey'
import { useInvoicesStore } from '@/stores/invoices'

const apiKeyStore = useApiKeyStore()
const invoicesStore = useInvoicesStore()

const analyzedInvoices = computed(() => invoicesStore.analyzedInvoices)
</script>