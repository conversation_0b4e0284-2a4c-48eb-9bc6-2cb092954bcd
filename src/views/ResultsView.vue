<template>
  <div class="max-w-full lg:max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8 space-y-8">
    <!-- Loading state -->
    <div v-if="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-accent-600"></div>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="card text-center">
      <ExclamationTriangleIcon class="mx-auto h-12 w-12 text-error-500" />
      <h3 class="mt-4 text-heading-3">Wystąpił błąd</h3>
      <p class="mt-2 text-body">{{ error }}</p>
      <div class="mt-6">
        <router-link to="/history" class="btn-primary">
          Powrót do historii
        </router-link>
      </div>
    </div>

    <!-- Results -->
    <div v-else-if="invoice && invoice.analysisData">
      <!-- Breadcrumb -->
      <nav class="flex" aria-label="Breadcrumb">
        <ol class="flex items-center space-x-4">
          <li>
            <router-link to="/" class="text-neutral-500 hover:text-neutral-700 transition-colors">
              Dashboard
            </router-link>
          </li>
          <li>
            <ChevronRightIcon class="h-5 w-5 text-neutral-300" />
          </li>
          <li>
            <router-link to="/history" class="text-neutral-500 hover:text-neutral-700 transition-colors">
              Historia
            </router-link>
          </li>
          <li>
            <ChevronRightIcon class="h-5 w-5 text-neutral-300" />
          </li>
          <li>
            <span class="text-neutral-700 font-medium">{{ invoice.fileName }}</span>
          </li>
        </ol>
      </nav>

      <!-- Kompaktowe metadata -->
      <div class="flex items-center justify-between py-4 px-6 bg-neutral-50 rounded-lg border border-neutral-200">
        <div>
          <p class="text-body font-medium text-neutral-900">{{ invoice.fileName }}</p>
          <p class="text-caption text-neutral-500">
            Przesłano: {{ formatDate(invoice.uploadDate) }}
          </p>
        </div>

        <div class="flex items-center px-3 py-1.5 bg-success-50 text-success-700 rounded-md border border-success-200">
          <CheckCircleIcon class="h-3 w-3 mr-1.5" />
          <span class="text-xs font-medium">Przeanalizowano</span>
        </div>
      </div>

      <!-- Analysis Results -->
      <InvoiceResults 
        :data="invoice.analysisData" 
        :fileName="invoice.fileName" 
      />
    </div>

    <!-- Not analyzed -->
    <div v-else-if="invoice">
      <div class="card text-center">
        <ClockIcon class="mx-auto h-12 w-12 text-warning-500" />
        <h3 class="mt-4 text-heading-3">Faktura nie została jeszcze przeanalizowana</h3>
        <p class="mt-2 text-body max-w-2xl mx-auto">
          Plik {{ invoice.fileName }} jest w kolejce do analizy lub analiza nie powiodła się.
        </p>
        <div class="mt-6 flex flex-col sm:flex-row gap-3 justify-center">
          <button
            @click="startAnalysis"
            :disabled="isAnalyzing"
            class="btn-primary"
          >
            <span v-if="isAnalyzing">Analizowanie...</span>
            <span v-else>Rozpocznij analizę</span>
          </button>

          <router-link to="/history" class="btn-secondary">
            Powrót do historii
          </router-link>
        </div>
      </div>
    </div>

    <!-- Progress during analysis -->
    <div v-if="analysisProgress > 0 && analysisProgress < 100" class="card">
      <div class="flex items-center space-x-4">
        <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-accent-600"></div>
        <div class="flex-1">
          <p class="text-sm font-semibold text-neutral-900">Analizowanie faktury...</p>
          <div class="mt-3 bg-neutral-200 rounded-full h-3">
            <div
              class="bg-gradient-to-r from-accent-500 to-accent-600 h-3 rounded-full transition-all duration-300"
              :style="{ width: `${analysisProgress}%` }"
            ></div>
          </div>
        </div>
        <span class="text-sm font-medium text-neutral-600">{{ analysisProgress }}%</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  ChevronRightIcon
} from '@heroicons/vue/24/outline'
import { useInvoicesStore } from '@/stores/invoices'
import { useInvoiceAnalysis } from '@/composables/useInvoiceAnalysis'
import InvoiceResults from '@/components/visualization/InvoiceResults.vue'
import type { InvoiceData } from '@/types'

interface Props {
  id: string
}

const props = defineProps<Props>()
const route = useRoute()
const router = useRouter()

const invoicesStore = useInvoicesStore()
const { analyzeFile, getProgress, isAnalyzing } = useInvoiceAnalysis()

const invoice = ref<InvoiceData | null>(null)
const loading = ref(true)
const error = ref('')
const originalFile = ref<File | null>(null)

const analysisProgress = computed(() => getProgress(props.id))

const formatDate = (date: Date): string => {
  return date.toLocaleString('pl-PL', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

async function loadInvoice() {
  loading.value = true
  error.value = ''

  try {
    const foundInvoice = invoicesStore.getInvoiceById(props.id)
    
    if (!foundInvoice) {
      error.value = 'Nie znaleziono faktury o podanym ID'
      return
    }

    invoice.value = foundInvoice
  } catch (err) {
    console.error('Error loading invoice:', err)
    error.value = 'Błąd podczas ładowania faktury'
  } finally {
    loading.value = false
  }
}

async function startAnalysis() {
  if (!invoice.value || !originalFile.value) {
    error.value = 'Brak pliku do analizy'
    return
  }

  try {
    error.value = ''
    await analyzeFile(originalFile.value, props.id)
    
    // Odświeź dane faktury
    await loadInvoice()
  } catch (err) {
    console.error('Analysis error:', err)
    error.value = err instanceof Error ? err.message : 'Błąd analizy'
  }
}


// Obserwuj zmiany ID w route
watch(() => props.id, loadInvoice, { immediate: true })

onMounted(() => {
  loadInvoice()
})
</script>