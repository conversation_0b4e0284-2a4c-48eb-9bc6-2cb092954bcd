import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type { ApiKeyStore } from '@/types'

export const useApiKeyStore = defineStore('apiKey', () => {
  const apiKey = ref<string | null>(null)
  const isValid = ref(false)
  const lastValidated = ref<Date | undefined>(undefined)

  const isConfigured = computed(() => !!apiKey.value)
  const maskedKey = computed(() => {
    if (!apiKey.value) return null
    const key = apiKey.value
    return key.length > 8 ? `${key.slice(0, 4)}...${key.slice(-4)}` : '••••••••'
  })

  function setApiKey(key: string) {
    apiKey.value = key
    localStorage.setItem('gemini_api_key', key)
  }

  function validateApiKey() {
    if (!apiKey.value) return false
    
    isValid.value = true
    lastValidated.value = new Date()
    
    localStorage.setItem('gemini_api_key_valid', 'true')
    localStorage.setItem('gemini_api_key_validated', new Date().toISOString())
    
    return true
  }

  function clearApiKey() {
    apiKey.value = null
    isValid.value = false
    lastValidated.value = undefined
    
    localStorage.removeItem('gemini_api_key')
    localStorage.removeItem('gemini_api_key_valid')
    localStorage.removeItem('gemini_api_key_validated')
  }

  function loadFromStorage() {
    const stored = localStorage.getItem('gemini_api_key')
    const storedValid = localStorage.getItem('gemini_api_key_valid')
    const storedValidated = localStorage.getItem('gemini_api_key_validated')
    
    if (stored) {
      apiKey.value = stored
      isValid.value = storedValid === 'true'
      lastValidated.value = storedValidated ? new Date(storedValidated) : undefined
    }
  }

  loadFromStorage()

  return {
    apiKey: readonly(apiKey),
    isValid: readonly(isValid),
    lastValidated: readonly(lastValidated),
    isConfigured,
    maskedKey,
    setApiKey,
    validateApiKey,
    clearApiKey,
    loadFromStorage
  }
})