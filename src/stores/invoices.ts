import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { InvoiceData, AnalysisResult } from '@/types'

export const useInvoicesStore = defineStore('invoices', () => {
  const invoices = ref<InvoiceData[]>([])
  const currentAnalysis = ref<string | null>(null)

  const analyzedInvoices = computed(() => 
    invoices.value.filter(invoice => invoice.analyzed)
  )

  const pendingInvoices = computed(() => 
    invoices.value.filter(invoice => !invoice.analyzed)
  )

  function addInvoice(file: File): string {
    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9)
    const invoice: InvoiceData = {
      id,
      fileName: file.name,
      uploadDate: new Date(),
      analyzed: false
    }
    
    invoices.value.push(invoice)
    saveToStorage()
    return id
  }

  function updateInvoiceAnalysis(id: string, analysisData: AnalysisResult) {
    const invoice = invoices.value.find(inv => inv.id === id)
    if (invoice) {
      invoice.analyzed = true
      invoice.analysisData = analysisData
      saveToStorage()
    }
  }

  function removeInvoice(id: string) {
    const index = invoices.value.findIndex(inv => inv.id === id)
    if (index > -1) {
      invoices.value.splice(index, 1)
      saveToStorage()
    }
  }

  function getInvoiceById(id: string): InvoiceData | undefined {
    return invoices.value.find(inv => inv.id === id)
  }

  function setCurrentAnalysis(id: string | null) {
    currentAnalysis.value = id
  }

  function clearAll() {
    invoices.value = []
    currentAnalysis.value = null
    localStorage.removeItem('invoices_data')
  }

  function saveToStorage() {
    localStorage.setItem('invoices_data', JSON.stringify(invoices.value))
  }

  function loadFromStorage() {
    const stored = localStorage.getItem('invoices_data')
    if (stored) {
      try {
        const parsed = JSON.parse(stored)
        invoices.value = parsed.map((inv: any) => ({
          ...inv,
          uploadDate: new Date(inv.uploadDate)
        }))
      } catch (error) {
        console.error('Error loading invoices from storage:', error)
      }
    }
  }

  loadFromStorage()

  return {
    invoices: computed(() => invoices.value),
    analyzedInvoices,
    pendingInvoices,
    currentAnalysis: computed(() => currentAnalysis.value),
    addInvoice,
    updateInvoiceAnalysis,
    removeInvoice,
    getInvoiceById,
    setCurrentAnalysis,
    clearAll,
    saveToStorage,
    loadFromStorage
  }
})