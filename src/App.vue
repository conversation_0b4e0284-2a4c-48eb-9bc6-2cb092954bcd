<template>
  <div class="min-h-screen">
    <!-- Modern header with glassmorphism effect -->
    <header class="sticky top-0 z-50 bg-white/80 backdrop-blur-md border-b border-neutral-200/50">
      <div class="max-w-7xl mx-auto px-6">
        <div class="flex items-center justify-between h-16">
          <!-- Logo -->
          <div class="flex items-center space-x-4">
            <div class="flex-shrink-0">
              <router-link to="/" class="flex items-center space-x-3 group">
                <div class="w-8 h-8 bg-gradient-to-br from-accent-500 to-accent-600 rounded-lg flex items-center justify-center transition-transform group-hover:scale-105">
                  <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                  </svg>
                </div>
                <span class="text-lg font-semibold text-neutral-900 group-hover:text-accent-600 transition-colors">
                  Invoice Analyzer
                </span>
              </router-link>
            </div>
          </div>

          <!-- Navigation -->
          <nav class="hidden md:flex items-center space-x-1">
            <router-link
              to="/"
              class="nav-item"
              :class="{ 'nav-item--active': $route.name === 'home' }"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z" />
              </svg>
              Dashboard
            </router-link>
            <router-link
              to="/upload"
              class="nav-item"
              :class="{ 'nav-item--active': $route.name === 'upload' }"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              Upload
            </router-link>
            <router-link
              to="/history"
              class="nav-item"
              :class="{ 'nav-item--active': $route.name === 'history' }"
            >
              <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              Historia
            </router-link>
          </nav>

          <!-- Settings -->
          <div class="flex items-center">
            <router-link
              to="/settings"
              class="p-2 rounded-lg text-neutral-500 hover:text-neutral-700 hover:bg-neutral-100 transition-all duration-200"
              :class="$route.name === 'settings' ? 'text-accent-600 bg-accent-50' : ''"
            >
              <CogIcon class="h-5 w-5" />
            </router-link>
          </div>
        </div>
      </div>
    </header>

    <!-- Main content with improved spacing -->
    <main class="max-w-7xl mx-auto px-6 py-8">
      <div class="animate-fade-in">
        <router-view />
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { CogIcon } from '@heroicons/vue/24/outline'
</script>