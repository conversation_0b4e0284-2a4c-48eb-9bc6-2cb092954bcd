# Energy Invoice Analyzer

**Analizator Faktur Energetycznych** - aplikacja webowa do automatycznego wyciągania i wizualizacji danych z faktur energetycznych przy użyciu Google Gemini AI.

## ✨ Główne funkcjonalności

- 🤖 **Analiza AI**: Automatyczne wyciąganie danych z faktur przy użyciu Google Gemini Flash 2.5
- 📤 **Upload plików**: Drag & drop dla PDF, JPG, PNG, WebP (max 10MB)
- 🔐 **Bezpieczny klucz API**: Lokalne przechowywanie klucza Google Gemini
- 📊 **Wizualizacja wyników**: Czytelne karty z danymi faktury
- 📜 **Historia**: Zarządzanie przesłanymi fakturami
- 📱 **Responsive**: Działa na desktop, tablet i mobile
- 💾 **Offline-first**: Wszystkie dane przechowywane lokalnie

## 🚀 Szybki start

### Wymagania
- Node.js 18+ 
- NPM lub Yarn
- Klucz API Google Gemini (bezpłatny na [Google AI Studio](https://makersuite.google.com/app/apikey))

### Instalacja

```bash
# Sklonuj repozytorium
git clone <repository-url>
cd energy-invoice-analyzer

# Zainstaluj zależności
npm install

# Uruchom serwer deweloperski
npm run dev

# Otwórz http://localhost:3000 w przeglądarce
```

### Pierwsza konfiguracja

1. Przejdź do sekcji **Ustawienia** (ikona koła zębatego)
2. Wklej swój klucz API Google Gemini
3. Zapisz ustawienia
4. Przejdź do **Upload** i prześlij swoją pierwszą fakturę!

## 🛠️ Komendy deweloperskie

```bash
# Serwer deweloperski z hot reload
npm run dev

# Budowa produkcyjna
npm run build

# Podgląd buildu produkcyjnego  
npm run preview

# Testy jednostkowe
npm run test:unit

# Linting i formatowanie
npm run lint

# Sprawdzanie typów TypeScript
npm run type-check
```

## 📋 Obsługiwane formaty faktur

Aplikacja analizuje faktury energetyczne i wyciąga następujące dane:

### 📊 Dostawca energii
- Nazwa firmy
- Adres 
- NIP

### 👤 Klient
- Nazwa/imię i nazwisko
- Adres
- Numer konta klienta

### 🧾 Szczegóły faktury
- Numer faktury
- Data wystawienia
- Termin płatności
- Okres rozliczeniowy

### ⚡ Dane energetyczne
- Stan poprzedni licznika
- Stan aktualny licznika  
- Zużycie w kWh
- Taryfa energetyczna
- Cena za kWh

### 💰 Dane finansowe
- Kwota netto
- VAT
- Kwota brutto
- Waluta

## 🔧 Architektura techniczna

### Frontend
- **Vue.js 3** + Composition API + TypeScript
- **Tailwind CSS** + HeadlessUI (stylowanie)
- **Pinia** (zarządzanie stanem)
- **Vue Router** (routing)
- **Vite** (build tool)

### Integracje
- **Google Gemini Flash 2.5** (analiza AI)
- **LocalStorage** (przechowywanie danych)
- **FileReader API** (obsługa plików)

### Struktura projektu
```
src/
├── components/          # Komponenty Vue
│   ├── upload/         # Upload plików
│   ├── visualization/  # Wyświetlanie wyników
│   └── common/         # Wspólne komponenty
├── composables/        # Logika biznesowa (Composition API)
├── stores/            # Pinia stores (stan aplikacji) 
├── utils/             # Utilities i API
├── types/             # TypeScript typy
└── views/             # Strony aplikacji
```

## 🔐 Bezpieczeństwo i prywatność

- **Lokalność danych**: Wszystkie dane (klucz API, faktury, wyniki) przechowywane tylko lokalnie
- **Brak zewnętrznych serwerów**: Komunikacja tylko z Google Gemini API
- **Bezpieczny klucz**: API key maskowany w interfejsie
- **HTTPS**: Wymagane w produkcji
- **Czyszczenie danych**: Możliwość usunięcia wszystkich danych lokalnych

## 📱 Responsywność

- **Mobile**: 320px-768px - focus na upload i wyniki
- **Tablet**: 768px-1024px - 2-kolumnowy layout  
- **Desktop**: 1024px+ - 3-kolumnowy layout z sidebar

## 🚀 Deployment

### Vercel (rekomendowane)
```bash
# Zainstaluj Vercel CLI
npm i -g vercel

# Deploy
vercel --prod
```

### Netlify
```bash
# Zbuduj projekt
npm run build

# Prześlij folder dist/ na Netlify
```

### Docker (opcjonalne)
```bash
# Zbuduj obraz
docker build -t energy-invoice-analyzer .

# Uruchom kontener
docker run -p 3000:80 energy-invoice-analyzer
```

## 🐛 Troubleshooting

### Błędy API
- Sprawdź poprawność klucza Google Gemini
- Upewnij się, że masz aktywne quota na API
- Sprawdź połączenie internetowe

### Problemy z uploadem
- Maksymalny rozmiar pliku: 10MB
- Obsługiwane formaty: PDF, JPG, PNG, WebP
- Upewnij się, że plik nie jest zaszyfrowany

### Błędy buildu
```bash
# Wyczyść cache i reinstaluj
rm -rf node_modules package-lock.json
npm install
npm run build
```

## 📈 Roadmap

### v1.1 - Enhancemenets
- 📊 Wykresy zużycia energii
- 🔍 Zaawansowane wyszukiwanie  
- 📱 PWA support
- 🌐 Internationalization (EN/PL)

### v1.2 - Integracje  
- 📧 Import z Gmail
- 🔗 API dla zewnętrznych systemów
- 📋 Operacje masowe
- 🤖 Smart templates

## 🤝 Kontrybuowanie

1. Sforkuj repozytorium
2. Stwórz feature branch (`git checkout -b feature/amazing-feature`)
3. Commituj zmiany (`git commit -m 'Add amazing feature'`)
4. Wypchnij branch (`git push origin feature/amazing-feature`)
5. Otwórz Pull Request

## 📄 Licencja

MIT License - szczegóły w pliku [LICENSE](LICENSE)

## 👨‍💻 Autor

Stworzono z ❤️ przy użyciu Vue.js i Google Gemini AI

---

**Uwaga**: Aplikacja wymaga klucza API Google Gemini do działania. Klucz jest przechowywany tylko lokalnie w przeglądarce.