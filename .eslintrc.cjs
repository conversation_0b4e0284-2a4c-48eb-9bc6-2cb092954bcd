/* eslint-env node */
require('@rushstack/eslint-patch/modern-module-resolution')

module.exports = {
  root: true,
  'extends': [
    'plugin:vue/vue3-essential',
    'eslint:recommended',
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier/skip-formatting'
  ],
  parserOptions: {
    ecmaVersion: 'latest'
  },
  rules: {
    // Wyłącz sprawdzanie unused vars dla TypeScript
    '@typescript-eslint/no-unused-vars': 'off',
    'vue/no-unused-vars': 'off',
    // Pozwól na komponenty jednoliterowe
    'vue/multi-word-component-names': 'off'
  }
}