{"name": "energy-invoice-analyzer", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "test:unit": "vitest", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"pinia": "^2.1.7", "vue": "^3.4.0", "vue-router": "^4.2.5"}, "devDependencies": {"@headlessui/vue": "^1.7.16", "@heroicons/vue": "^2.0.18", "@rushstack/eslint-patch": "^1.3.3", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/vite": "^4.1.13", "@tsconfig/node18": "^18.2.2", "@types/node": "^18.18.5", "@vitejs/plugin-vue": "^5.2.4", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.1", "@vue/tsconfig": "^0.4.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "jsdom": "^22.1.0", "npm-run-all": "^4.1.5", "tailwindcss": "^4.0.0", "typescript": "~5.2.0", "vite": "^5.4.20", "vitest": "^0.34.6", "vue-tsc": "^1.8.19"}}